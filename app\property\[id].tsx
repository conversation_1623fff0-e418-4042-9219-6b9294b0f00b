import { useAuth } from '@/contexts/AuthContext';
import { useFavorites } from '@/contexts/FavoritesContext';
import { useMessaging } from '@/contexts/MessagingContext';
import { PropertyService } from '@/services/propertyService';
import { Property } from '@/types';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

export default function PropertyDetailsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuth();
  const { isPropertyFavorited, addToFavorites, removeFromFavorites } = useFavorites();
  const { createConversation } = useMessaging();
  const [property, setProperty] = useState<Property | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    if (id) {
      loadProperty();
    }
  }, [id]);

  const loadProperty = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const result = await PropertyService.getPropertyById(id);
      if (result.success && result.data) {
        setProperty(result.data);
        // Increment view count
        PropertyService.incrementViews(id);
      } else {
        Alert.alert('Error', 'Property not found');
        router.back();
      }
    } catch (error) {
      console.error('Error loading property:', error);
      Alert.alert('Error', 'Failed to load property details');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleContactOwner = async () => {
    if (!property || !user) return;

    // Don't allow contacting yourself
    if (property.ownerId === user.id) {
      Alert.alert('Info', 'This is your own property listing');
      return;
    }

    try {
      // Create or get existing conversation
      const conversation = await createConversation(
        property.ownerId,
        'Property Owner', // We'll need to get the actual owner name from user service
        property.id,
        property.title
      );

      if (conversation) {
        router.push({
          pathname: '/chat/[conversationId]',
          params: {
            conversationId: conversation.id,
            participantId: property.ownerId,
            participantName: 'Property Owner',
            propertyId: property.id,
            propertyTitle: property.title,
          },
        });
      } else {
        Alert.alert('Error', 'Failed to start conversation');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to contact owner');
    }
  };

  const handleSaveProperty = async () => {
    if (!property || !user) return;

    const isFavorited = isPropertyFavorited(property.id);

    try {
      if (isFavorited) {
        await removeFromFavorites(property.id);
        Alert.alert('Success', 'Property removed from favorites');
      } else {
        await addToFavorites(property.id);
        Alert.alert('Success', 'Property added to favorites');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorites');
    }
  };

  const handleScheduleViewing = () => {
    Alert.alert('Schedule Viewing', 'Viewing scheduling will be implemented');
  };

  const formatPrice = (price: number) => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} L`;
    } else {
      return `₹${price.toLocaleString()}`;
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading property details...</Text>
      </View>
    );
  }

  if (!property) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Property not found</Text>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleSaveProperty}>
          <Text style={styles.saveButtonText}>
            {property && isPropertyFavorited(property.id) ? '♥ Saved' : '♡ Save'}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Image Gallery */}
        <View style={styles.imageContainer}>
          {property.images && property.images.length > 0 ? (
            <ScrollView horizontal pagingEnabled showsHorizontalScrollIndicator={false}>
              {property.images.map((image, index) => (
                <Image key={index} source={{ uri: image }} style={styles.propertyImage} />
              ))}
            </ScrollView>
          ) : (
            <View style={styles.placeholderImage}>
              <Text style={styles.placeholderText}>No Images Available</Text>
            </View>
          )}
        </View>

        {/* Property Info */}
        <View style={styles.infoSection}>
          <Text style={styles.propertyTitle}>{property.title}</Text>
          <Text style={styles.propertyPrice}>{formatPrice(property.price)}</Text>
          <Text style={styles.propertyLocation}>
            {property.location.address}, {property.location.city}, {property.location.state}
          </Text>
        </View>

        {/* Property Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Property Details</Text>
          <View style={styles.featuresGrid}>
            <View style={styles.featureItem}>
              <Text style={styles.featureLabel}>Type</Text>
              <Text style={styles.featureValue}>{property.propertyType}</Text>
            </View>
            <View style={styles.featureItem}>
              <Text style={styles.featureLabel}>Area</Text>
              <Text style={styles.featureValue}>
                {property.features.area} {property.features.areaUnit}
              </Text>
            </View>
            {property.features.bedrooms && (
              <View style={styles.featureItem}>
                <Text style={styles.featureLabel}>Bedrooms</Text>
                <Text style={styles.featureValue}>{property.features.bedrooms}</Text>
              </View>
            )}
            {property.features.bathrooms && (
              <View style={styles.featureItem}>
                <Text style={styles.featureLabel}>Bathrooms</Text>
                <Text style={styles.featureValue}>{property.features.bathrooms}</Text>
              </View>
            )}
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{property.description}</Text>
        </View>

        {/* RERA Details */}
        {property.reraDetails && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>RERA Information</Text>
            <View style={styles.reraContainer}>
              <Text style={styles.reraLabel}>Registration Number:</Text>
              <Text style={styles.reraValue}>{property.reraDetails.registrationNumber}</Text>
              {property.reraDetails.projectName && (
                <>
                  <Text style={styles.reraLabel}>Project Name:</Text>
                  <Text style={styles.reraValue}>{property.reraDetails.projectName}</Text>
                </>
              )}
              {property.reraDetails.developerName && (
                <>
                  <Text style={styles.reraLabel}>Developer:</Text>
                  <Text style={styles.reraValue}>{property.reraDetails.developerName}</Text>
                </>
              )}
            </View>
          </View>
        )}

        {/* Legal Documents */}
        {property.legalDocuments && property.legalDocuments.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Legal Documents</Text>
            {property.legalDocuments.map((doc, index) => (
              <TouchableOpacity key={index} style={styles.documentItem}>
                <Text style={styles.documentName}>{doc.name}</Text>
                <Text style={styles.documentType}>{doc.type}</Text>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Property Stats */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Property Statistics</Text>
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{property.views || 0}</Text>
              <Text style={styles.statLabel}>Views</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{property.inquiries || 0}</Text>
              <Text style={styles.statLabel}>Inquiries</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      {property && user && property.ownerId !== user.id && (
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.inquiryButton}
            onPress={() => router.push({
              pathname: '/inquiry/create',
              params: {
                propertyId: property.id,
                propertyTitle: property.title,
                sellerId: property.ownerId,
                sellerName: 'Property Owner',
              },
            })}
          >
            <Text style={styles.inquiryButtonText}>Send Inquiry</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.offerButton}
            onPress={() => router.push({
              pathname: '/offer/create',
              params: {
                propertyId: property.id,
                propertyTitle: property.title,
                sellerId: property.ownerId,
                sellerName: 'Property Owner',
                originalPrice: property.price.toString(),
              },
            })}
          >
            <Text style={styles.offerButtonText}>Make Offer</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.contactButton} onPress={handleContactOwner}>
            <Text style={styles.contactButtonText}>Message</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#ff4444',
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    paddingTop: 50,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  saveButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    height: 250,
    backgroundColor: '#f0f0f0',
  },
  propertyImage: {
    width: 400, // Adjust based on screen width
    height: 250,
    resizeMode: 'cover',
  },
  placeholderImage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  infoSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  propertyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  propertyPrice: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  propertyLocation: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  featuresGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  featureItem: {
    width: '50%',
    marginBottom: 16,
  },
  featureLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  featureValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  description: {
    fontSize: 16,
    color: '#333',
    lineHeight: 24,
  },
  reraContainer: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
  },
  reraLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  reraValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 12,
  },
  documentItem: {
    backgroundColor: '#f9f9f9',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  documentType: {
    fontSize: 14,
    color: '#666',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
  },
  actionContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  inquiryButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginRight: 6,
  },
  inquiryButtonText: {
    color: '#333',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  offerButton: {
    flex: 1,
    backgroundColor: '#28a745',
    padding: 12,
    borderRadius: 8,
    marginHorizontal: 6,
  },
  offerButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  contactButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 12,
    borderRadius: 8,
    marginLeft: 6,
  },
  contactButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});
