import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { InquiryService } from '@/services/inquiryService';

export default function CreateInquiryScreen() {
  const { propertyId, propertyTitle, sellerId, sellerName } = useLocalSearchParams<{
    propertyId: string;
    propertyTitle: string;
    sellerId: string;
    sellerName: string;
  }>();

  const { user } = useAuth();
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmitInquiry = async () => {
    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your inquiry message');
      return;
    }

    if (!user || !propertyId || !sellerId) {
      Alert.alert('Error', 'Missing required information');
      return;
    }

    setLoading(true);
    try {
      const result = await InquiryService.createInquiry(
        propertyId,
        propertyTitle || 'Property',
        user.id,
        user.name,
        sellerId,
        sellerName || 'Property Owner',
        message.trim()
      );

      if (result.success) {
        Alert.alert(
          'Success',
          'Your inquiry has been sent to the property owner. They will be notified and can respond to you directly.',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to send inquiry');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to send inquiry');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Send Inquiry</Text>
        <View style={{ width: 50 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.propertyInfo}>
          <Text style={styles.propertyTitle}>{propertyTitle}</Text>
          <Text style={styles.sellerInfo}>To: {sellerName}</Text>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Your Inquiry</Text>
          <Text style={styles.description}>
            Send a message to the property owner about this listing. They will receive your inquiry and can respond directly.
          </Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Message *</Text>
            <TextInput
              style={styles.textArea}
              value={message}
              onChangeText={setMessage}
              placeholder="Hi, I'm interested in this property. Could you please provide more details about..."
              multiline
              numberOfLines={8}
              textAlignVertical="top"
              maxLength={1000}
            />
            <Text style={styles.characterCount}>{message.length}/1000</Text>
          </View>

          <View style={styles.tipContainer}>
            <Text style={styles.tipTitle}>💡 Tips for a good inquiry:</Text>
            <Text style={styles.tipText}>• Be specific about what you want to know</Text>
            <Text style={styles.tipText}>• Mention your timeline if you're serious</Text>
            <Text style={styles.tipText}>• Ask about viewing arrangements</Text>
            <Text style={styles.tipText}>• Include your contact preferences</Text>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmitInquiry}
            disabled={loading || !message.trim()}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Sending...' : 'Send Inquiry'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  backButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  propertyInfo: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  propertyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sellerInfo: {
    fontSize: 16,
    color: '#666',
  },
  formSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    height: 120,
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  tipContainer: {
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  submitButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
