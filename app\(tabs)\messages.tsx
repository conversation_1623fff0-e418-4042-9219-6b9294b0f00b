import { useAuth } from '@/contexts/AuthContext';
import { useMessaging } from '@/contexts/MessagingContext';
import { Conversation } from '@/services/messagingService';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function MessagesScreen() {
  const { user } = useAuth();
  const { conversations, loading } = useMessaging();
  const [searchQuery, setSearchQuery] = useState('');

  const filteredConversations = conversations.filter((conversation) => {
    const otherParticipantName = getOtherParticipantName(conversation);
    return (
      otherParticipantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conversation.lastMessage.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (conversation.propertyTitle &&
        conversation.propertyTitle.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  const getOtherParticipantName = (conversation: Conversation): string => {
    if (!user) return '';
    const otherParticipantId = conversation.participants.find(id => id !== user.id);
    return otherParticipantId ? conversation.participantNames[otherParticipantId] || 'Unknown User' : '';
  };

  const getOtherParticipantId = (conversation: Conversation): string => {
    if (!user) return '';
    return conversation.participants.find(id => id !== user.id) || '';
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return `${days}d ago`;
    }
  };

  const handleConversationPress = (conversation: Conversation) => {
    const otherParticipantId = getOtherParticipantId(conversation);
    const otherParticipantName = getOtherParticipantName(conversation);

    router.push({
      pathname: '/chat/[conversationId]',
      params: {
        conversationId: conversation.id,
        participantId: otherParticipantId,
        participantName: otherParticipantName,
        propertyId: conversation.propertyId || '',
        propertyTitle: conversation.propertyTitle || '',
      },
    });
  };

  const renderConversationItem = ({ item }: { item: Conversation }) => {
    const otherParticipantName = getOtherParticipantName(item);
    const unreadCount = user ? item.unreadCount[user.id] || 0 : 0;
    const hasUnread = unreadCount > 0;

    return (
      <TouchableOpacity style={styles.messageCard} onPress={() => handleConversationPress(item)}>
        <View style={styles.messageHeader}>
          <View style={styles.messageInfo}>
            <Text style={[styles.senderName, hasUnread && styles.unreadText]}>
              {otherParticipantName}
            </Text>
            <Text style={styles.timestamp}>{formatTimestamp(item.lastMessageTime)}</Text>
          </View>
          {hasUnread && (
            <View style={styles.unreadBadge}>
              <Text style={styles.unreadBadgeText}>{unreadCount}</Text>
            </View>
          )}
        </View>

        {item.propertyTitle && (
          <Text style={styles.propertyTitle}>Re: {item.propertyTitle}</Text>
        )}

        <Text
          style={[styles.lastMessage, hasUnread && styles.unreadText]}
          numberOfLines={2}
        >
          {item.lastMessage || 'No messages yet'}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Text style={styles.emptyStateTitle}>No Messages</Text>
      <Text style={styles.emptyStateText}>
        Your conversations will appear here when you start messaging with other users.
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Messages</Text>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search messages..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading conversations...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredConversations}
          renderItem={renderConversationItem}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={
            filteredConversations.length === 0 ? styles.emptyContainer : undefined
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  messagesList: {
    flex: 1,
  },
  messageCard: {
    backgroundColor: '#fff',
    padding: 16,
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  senderName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    marginLeft: 8,
  },
  unreadBadge: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  unreadBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  propertyTitle: {
    fontSize: 14,
    color: '#007AFF',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  unreadText: {
    fontWeight: 'bold',
    color: '#333',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyState: {
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});
