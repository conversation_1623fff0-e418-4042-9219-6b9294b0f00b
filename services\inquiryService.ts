import { db } from '@/config/firebase';
import { ApiResponse } from '@/types';
import {
    addDoc,
    collection,
    doc,
    getDocs,
    onSnapshot,
    orderBy,
    query,
    serverTimestamp,
    updateDoc,
    where,
} from 'firebase/firestore';

export interface Inquiry {
  id: string;
  propertyId: string;
  propertyTitle: string;
  buyerId: string;
  buyerName: string;
  sellerId: string;
  sellerName: string;
  agentId?: string;
  message: string;
  status: 'pending' | 'responded' | 'closed';
  createdAt: Date;
  updatedAt: Date;
  responses?: InquiryResponse[];
}

export interface InquiryResponse {
  id: string;
  inquiryId: string;
  message: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
}

export interface Offer {
  id: string;
  propertyId: string;
  propertyTitle: string;
  buyerId: string;
  buyerName: string;
  sellerId: string;
  sellerName: string;
  amount: number;
  originalPrice: number;
  status: 'pending' | 'accepted' | 'rejected' | 'countered' | 'withdrawn';
  message?: string;
  counterOffer?: {
    amount: number;
    message: string;
    timestamp: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
}

export class InquiryService {
  private static readonly INQUIRIES_COLLECTION = 'inquiries';
  private static readonly OFFERS_COLLECTION = 'offers';
  private static readonly RESPONSES_COLLECTION = 'inquiry_responses';

  // Create a new inquiry
  static async createInquiry(
    propertyId: string,
    propertyTitle: string,
    buyerId: string,
    buyerName: string,
    sellerId: string,
    sellerName: string,
    message: string,
    agentId?: string
  ): Promise<ApiResponse<Inquiry>> {
    try {
      const inquiryData = {
        propertyId,
        propertyTitle,
        buyerId,
        buyerName,
        sellerId,
        sellerName,
        agentId: agentId || undefined,
        message,
        status: 'pending' as const,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      const docRef = await addDoc(collection(db, this.INQUIRIES_COLLECTION), inquiryData);

      const newInquiry: Inquiry = {
        id: docRef.id,
        ...inquiryData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return {
        success: true,
        data: newInquiry,
        message: 'Inquiry sent successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Create a new offer
  static async createOffer(
    propertyId: string,
    propertyTitle: string,
    buyerId: string,
    buyerName: string,
    sellerId: string,
    sellerName: string,
    amount: number,
    originalPrice: number,
    message?: string,
    expirationDays: number = 7
  ): Promise<ApiResponse<Offer>> {
    try {
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expirationDays);

      const offerData = {
        propertyId,
        propertyTitle,
        buyerId,
        buyerName,
        sellerId,
        sellerName,
        amount,
        originalPrice,
        status: 'pending' as const,
        message: message || '',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        expiresAt,
      };

      const docRef = await addDoc(collection(db, this.OFFERS_COLLECTION), offerData);

      const newOffer: Offer = {
        id: docRef.id,
        ...offerData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      return {
        success: true,
        data: newOffer,
        message: 'Offer submitted successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Get inquiries for a property
  static async getPropertyInquiries(propertyId: string): Promise<ApiResponse<Inquiry[]>> {
    try {
      const q = query(
        collection(db, this.INQUIRIES_COLLECTION),
        where('propertyId', '==', propertyId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const inquiries: Inquiry[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        inquiries.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          agentId: data.agentId,
          message: data.message,
          status: data.status,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          responses: data.responses || [],
        });
      });

      return {
        success: true,
        data: inquiries,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Get inquiries for a user (buyer or seller)
  static async getUserInquiries(userId: string, userType: 'buyer' | 'seller'): Promise<ApiResponse<Inquiry[]>> {
    try {
      const field = userType === 'buyer' ? 'buyerId' : 'sellerId';
      const q = query(
        collection(db, this.INQUIRIES_COLLECTION),
        where(field, '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const inquiries: Inquiry[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        inquiries.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          agentId: data.agentId,
          message: data.message,
          status: data.status,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          responses: data.responses || [],
        });
      });

      return {
        success: true,
        data: inquiries,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Get offers for a property
  static async getPropertyOffers(propertyId: string): Promise<ApiResponse<Offer[]>> {
    try {
      const q = query(
        collection(db, this.OFFERS_COLLECTION),
        where('propertyId', '==', propertyId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const offers: Offer[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        offers.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          amount: data.amount,
          originalPrice: data.originalPrice,
          status: data.status,
          message: data.message,
          counterOffer: data.counterOffer,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
        });
      });

      return {
        success: true,
        data: offers,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Get offers for a user (buyer or seller)
  static async getUserOffers(userId: string, userType: 'buyer' | 'seller'): Promise<ApiResponse<Offer[]>> {
    try {
      const field = userType === 'buyer' ? 'buyerId' : 'sellerId';
      const q = query(
        collection(db, this.OFFERS_COLLECTION),
        where(field, '==', userId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const offers: Offer[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        offers.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          amount: data.amount,
          originalPrice: data.originalPrice,
          status: data.status,
          message: data.message,
          counterOffer: data.counterOffer,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
        });
      });

      return {
        success: true,
        data: offers,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Update offer status
  static async updateOfferStatus(
    offerId: string,
    status: 'accepted' | 'rejected' | 'countered' | 'withdrawn',
    counterOffer?: { amount: number; message: string }
  ): Promise<ApiResponse<void>> {
    try {
      const offerRef = doc(db, this.OFFERS_COLLECTION, offerId);
      const updateData: any = {
        status,
        updatedAt: serverTimestamp(),
      };

      if (counterOffer) {
        updateData.counterOffer = {
          ...counterOffer,
          timestamp: serverTimestamp(),
        };
      }

      await updateDoc(offerRef, updateData);

      return {
        success: true,
        message: 'Offer updated successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Update inquiry status
  static async updateInquiryStatus(
    inquiryId: string,
    status: 'responded' | 'closed'
  ): Promise<ApiResponse<void>> {
    try {
      const inquiryRef = doc(db, this.INQUIRIES_COLLECTION, inquiryId);
      await updateDoc(inquiryRef, {
        status,
        updatedAt: serverTimestamp(),
      });

      return {
        success: true,
        message: 'Inquiry updated successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Subscribe to real-time inquiries for a user
  static subscribeToUserInquiries(
    userId: string,
    userType: 'buyer' | 'seller',
    callback: (inquiries: Inquiry[]) => void
  ): () => void {
    const field = userType === 'buyer' ? 'buyerId' : 'sellerId';
    const q = query(
      collection(db, this.INQUIRIES_COLLECTION),
      where(field, '==', userId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (querySnapshot) => {
      const inquiries: Inquiry[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        inquiries.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          agentId: data.agentId,
          message: data.message,
          status: data.status,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          responses: data.responses || [],
        });
      });
      callback(inquiries);
    });
  }

  // Subscribe to real-time offers for a user
  static subscribeToUserOffers(
    userId: string,
    userType: 'buyer' | 'seller',
    callback: (offers: Offer[]) => void
  ): () => void {
    const field = userType === 'buyer' ? 'buyerId' : 'sellerId';
    const q = query(
      collection(db, this.OFFERS_COLLECTION),
      where(field, '==', userId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (querySnapshot) => {
      const offers: Offer[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        offers.push({
          id: doc.id,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          buyerId: data.buyerId,
          buyerName: data.buyerName,
          sellerId: data.sellerId,
          sellerName: data.sellerName,
          amount: data.amount,
          originalPrice: data.originalPrice,
          status: data.status,
          message: data.message,
          counterOffer: data.counterOffer,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
        });
      });
      callback(offers);
    });
  }
}
