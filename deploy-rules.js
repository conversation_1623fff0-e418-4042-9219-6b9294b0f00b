// Script to deploy Firestore rules
// Run this with: node deploy-rules.js

const { exec } = require('child_process');

console.log('Deploying Firestore rules...');

exec('firebase deploy --only firestore:rules', (error, stdout, stderr) => {
  if (error) {
    console.error('Error deploying rules:', error);
    console.error('Make sure you have Firebase CLI installed and are logged in:');
    console.error('1. npm install -g firebase-tools');
    console.error('2. firebase login');
    console.error('3. firebase init (if not already initialized)');
    return;
  }
  
  if (stderr) {
    console.error('stderr:', stderr);
  }
  
  console.log('Rules deployment output:', stdout);
  console.log('Firestore rules deployed successfully!');
});
