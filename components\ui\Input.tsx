import React, { useState } from 'react';
import {
  TextInput,
  View,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing, Typography, Shadows } from '@/constants/Design';
import { useColorScheme } from '@/hooks/useColorScheme';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  containerStyle,
  inputStyle,
  labelStyle,
  onFocus,
  onBlur,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: error ? colors.error : isFocused ? colors.inputFocus : colors.inputBorder,
      borderRadius: BorderRadius.md,
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Spacing.base,
    };

    if (isFocused) {
      return {
        ...baseStyle,
        ...Shadows.sm,
      };
    }

    return baseStyle;
  };

  const getInputStyle = (): TextStyle => ({
    flex: 1,
    paddingVertical: Spacing.md,
    fontSize: Typography.fontSize.base,
    color: colors.text,
    fontWeight: Typography.fontWeight.normal,
  });

  const getLabelStyle = (): TextStyle => ({
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    color: colors.text,
    marginBottom: Spacing.sm,
  });

  const getErrorStyle = (): TextStyle => ({
    fontSize: Typography.fontSize.xs,
    color: colors.error,
    marginTop: Spacing.xs,
    fontWeight: Typography.fontWeight.medium,
  });

  const getHintStyle = (): TextStyle => ({
    fontSize: Typography.fontSize.xs,
    color: colors.textMuted,
    marginTop: Spacing.xs,
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <Text style={[getLabelStyle(), labelStyle]}>{label}</Text>
      )}
      
      <View style={getInputContainerStyle()}>
        {leftIcon && (
          <View style={styles.iconContainer}>{leftIcon}</View>
        )}
        
        <TextInput
          style={[getInputStyle(), inputStyle]}
          placeholderTextColor={colors.inputPlaceholder}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...textInputProps}
        />
        
        {rightIcon && (
          <View style={styles.iconContainer}>{rightIcon}</View>
        )}
      </View>
      
      {error && <Text style={getErrorStyle()}>{error}</Text>}
      {hint && !error && <Text style={getHintStyle()}>{hint}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.base,
  },
  iconContainer: {
    marginHorizontal: Spacing.xs,
  },
});
