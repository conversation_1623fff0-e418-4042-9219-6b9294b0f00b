import { useAuth } from '@/contexts/AuthContext';
import { PropertyService } from '@/services/propertyService';
import { LegalDocument, PropertyType } from '@/types';
import { Picker } from '@react-native-picker/picker';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export default function CreatePropertyScreen() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  // Basic property information
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [propertyType, setPropertyType] = useState<PropertyType>('flat');
  const [price, setPrice] = useState('');

  // Location information
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [pincode, setPincode] = useState('');

  // Property features
  const [bedrooms, setBedrooms] = useState('');
  const [bathrooms, setBathrooms] = useState('');
  const [area, setArea] = useState('');
  const [areaUnit, setAreaUnit] = useState<'sqft' | 'sqm' | 'acres'>('sqft');

  // RERA details
  const [reraNumber, setReraNumber] = useState('');
  const [projectName, setProjectName] = useState('');
  const [developerName, setDeveloperName] = useState('');

  // Images and documents
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [legalDocuments, setLegalDocuments] = useState<LegalDocument[]>([]);

  const propertyTypes: { label: string; value: PropertyType }[] = [
    { label: 'Flat/Apartment', value: 'flat' },
    { label: 'Plot', value: 'plot' },
    { label: 'Building with Land', value: 'building_with_land' },
    { label: 'Commercial', value: 'commercial' },
    { label: 'Rental', value: 'rental' },
  ];

  const areaUnits = [
    { label: 'Square Feet', value: 'sqft' },
    { label: 'Square Meters', value: 'sqm' },
    { label: 'Acres', value: 'acres' },
  ];

  const handleImagePicker = () => {
    // TODO: Implement image picker functionality
    Alert.alert('Image Picker', 'Image picker functionality will be implemented');
  };

  const handleDocumentPicker = () => {
    // TODO: Implement document picker functionality
    Alert.alert('Document Picker', 'Document picker functionality will be implemented');
  };

  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter property title');
      return false;
    }
    if (!description.trim()) {
      Alert.alert('Error', 'Please enter property description');
      return false;
    }
    if (!price.trim() || isNaN(Number(price))) {
      Alert.alert('Error', 'Please enter valid price');
      return false;
    }
    if (!address.trim() || !city.trim() || !state.trim() || !pincode.trim()) {
      Alert.alert('Error', 'Please fill all location fields');
      return false;
    }
    if (!area.trim() || isNaN(Number(area))) {
      Alert.alert('Error', 'Please enter valid area');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !user) return;

    setLoading(true);
    try {
      const propertyData = {
        title: title.trim(),
        description: description.trim(),
        propertyType,
        status: 'available' as const,
        price: Number(price),
        location: {
          address: address.trim(),
          city: city.trim(),
          state: state.trim(),
          pincode: pincode.trim(),
        },
        features: {
          bedrooms: bedrooms ? Number(bedrooms) : undefined,
          bathrooms: bathrooms ? Number(bathrooms) : undefined,
          area: Number(area),
          areaUnit,
          amenities: [],
        },
        images: selectedImages,
        legalDocuments,
        reraDetails: reraNumber ? {
          registrationNumber: reraNumber,
          projectName: projectName || '',
          developerName: developerName || '',
          completionDate: new Date(),
          verified: false,
        } : undefined,
        ownerId: user.id,
      };

      const result = await PropertyService.createProperty(propertyData);

      if (result.success) {
        Alert.alert('Success', 'Property listed successfully!', [
          { text: 'OK', onPress: () => router.back() },
        ]);
      } else {
        Alert.alert('Error', result.error || 'Failed to create property');
      }
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Listing</Text>
        <View style={{ width: 50 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Basic Information</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Property Title *</Text>
            <TextInput
              style={styles.input}
              value={title}
              onChangeText={setTitle}
              placeholder="e.g., 3 BHK Apartment in Bandra"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={description}
              onChangeText={setDescription}
              placeholder="Describe your property..."
              multiline
              numberOfLines={4}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Property Type *</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={propertyType}
                onValueChange={(itemValue) => setPropertyType(itemValue)}
                style={styles.picker}
              >
                {propertyTypes.map((type) => (
                  <Picker.Item
                    key={type.value}
                    label={type.label}
                    value={type.value}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Price (₹) *</Text>
            <TextInput
              style={styles.input}
              value={price}
              onChangeText={setPrice}
              placeholder="Enter price in rupees"
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Location</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Address *</Text>
            <TextInput
              style={styles.input}
              value={address}
              onChangeText={setAddress}
              placeholder="Enter full address"
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={styles.label}>City *</Text>
              <TextInput
                style={styles.input}
                value={city}
                onChangeText={setCity}
                placeholder="City"
              />
            </View>
            <View style={[styles.inputGroup, { flex: 1 }]}>
              <Text style={styles.label}>State *</Text>
              <TextInput
                style={styles.input}
                value={state}
                onChangeText={setState}
                placeholder="State"
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Pincode *</Text>
            <TextInput
              style={styles.input}
              value={pincode}
              onChangeText={setPincode}
              placeholder="Pincode"
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Property Features</Text>

          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 1, marginRight: 10 }]}>
              <Text style={styles.label}>Bedrooms</Text>
              <TextInput
                style={styles.input}
                value={bedrooms}
                onChangeText={setBedrooms}
                placeholder="Number of bedrooms"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.inputGroup, { flex: 1 }]}>
              <Text style={styles.label}>Bathrooms</Text>
              <TextInput
                style={styles.input}
                value={bathrooms}
                onChangeText={setBathrooms}
                placeholder="Number of bathrooms"
                keyboardType="numeric"
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, { flex: 2, marginRight: 10 }]}>
              <Text style={styles.label}>Area *</Text>
              <TextInput
                style={styles.input}
                value={area}
                onChangeText={setArea}
                placeholder="Property area"
                keyboardType="numeric"
              />
            </View>
            <View style={[styles.inputGroup, { flex: 1 }]}>
              <Text style={styles.label}>Unit</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={areaUnit}
                  onValueChange={(itemValue) => setAreaUnit(itemValue)}
                  style={styles.picker}
                >
                  {areaUnits.map((unit) => (
                    <Picker.Item
                      key={unit.value}
                      label={unit.label}
                      value={unit.value}
                    />
                  ))}
                </Picker>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>RERA Details (Optional)</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>RERA Registration Number</Text>
            <TextInput
              style={styles.input}
              value={reraNumber}
              onChangeText={setReraNumber}
              placeholder="Enter RERA number"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Project Name</Text>
            <TextInput
              style={styles.input}
              value={projectName}
              onChangeText={setProjectName}
              placeholder="Enter project name"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Developer Name</Text>
            <TextInput
              style={styles.input}
              value={developerName}
              onChangeText={setDeveloperName}
              placeholder="Enter developer name"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Images & Documents</Text>

          <TouchableOpacity style={styles.uploadButton} onPress={handleImagePicker}>
            <Text style={styles.uploadButtonText}>Add Property Images</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.uploadButton} onPress={handleDocumentPicker}>
            <Text style={styles.uploadButtonText}>Upload Legal Documents</Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, loading && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={loading}
        >
          <Text style={styles.submitButtonText}>
            {loading ? 'Creating Listing...' : 'Create Listing'}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  backButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  row: {
    flexDirection: 'row',
  },
  uploadButton: {
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#ddd',
    borderStyle: 'dashed',
  },
  uploadButtonText: {
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    margin: 16,
    padding: 16,
    borderRadius: 8,
    marginBottom: 32,
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});
