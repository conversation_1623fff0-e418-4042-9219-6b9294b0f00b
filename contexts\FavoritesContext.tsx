import React, { createContext, useContext, useEffect, useState } from 'react';
import { FavoritesService } from '@/services/favoritesService';
import { useAuth } from './AuthContext';

interface FavoritesContextType {
  favoriteIds: string[];
  loading: boolean;
  addToFavorites: (propertyId: string) => Promise<void>;
  removeFromFavorites: (propertyId: string) => Promise<void>;
  isPropertyFavorited: (propertyId: string) => boolean;
  refreshFavorites: () => Promise<void>;
}

const FavoritesContext = createContext<FavoritesContextType | undefined>(undefined);

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (context === undefined) {
    throw new Error('useFavorites must be used within a FavoritesProvider');
  }
  return context;
};

interface FavoritesProviderProps {
  children: React.ReactNode;
}

export const FavoritesProvider: React.FC<FavoritesProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [favoriteIds, setFavoriteIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadFavorites();
    } else {
      setFavoriteIds([]);
    }
  }, [user]);

  const loadFavorites = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const result = await FavoritesService.getFavoritePropertyIds(user.id);
      if (result.success && result.data) {
        setFavoriteIds(result.data);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToFavorites = async (propertyId: string) => {
    if (!user) return;

    try {
      const result = await FavoritesService.addToFavorites(user.id, propertyId);
      if (result.success) {
        setFavoriteIds(prev => [...prev, propertyId]);
      }
    } catch (error) {
      console.error('Error adding to favorites:', error);
    }
  };

  const removeFromFavorites = async (propertyId: string) => {
    if (!user) return;

    try {
      const result = await FavoritesService.removeFromFavorites(user.id, propertyId);
      if (result.success) {
        setFavoriteIds(prev => prev.filter(id => id !== propertyId));
      }
    } catch (error) {
      console.error('Error removing from favorites:', error);
    }
  };

  const isPropertyFavorited = (propertyId: string): boolean => {
    return favoriteIds.includes(propertyId);
  };

  const refreshFavorites = async () => {
    await loadFavorites();
  };

  const value: FavoritesContextType = {
    favoriteIds,
    loading,
    addToFavorites,
    removeFromFavorites,
    isPropertyFavorited,
    refreshFavorites,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};
