# Authentication System Testing Checklist

## Pre-Testing Setup

### 1. Ensure Firebase Rules are Deployed
```bash
firebase deploy --only firestore:rules
```

### 2. Clear App Data (Optional)
- Clear app cache/data to start fresh
- Or use different test email addresses

### 3. Check Firebase Console Access
- Firebase Authentication console
- Firestore database console

---

## 1. SIGN UP TESTING

### Test Users to Create:
| User Type | Name | Email | Password | Expected Features |
|-----------|------|-------|----------|-------------------|
| Buyer | John Buyer | <EMAIL> | testpass123 | Search Properties, View Favorites |
| Seller | Jane Seller | <EMAIL> | testpass123 | Create Listing, Manage Properties |
| Agent | Mike Agent | <EMAIL> | testpass123 | Create Listing, CRM Dashboard |
| Property Manager | Sarah Manager | <EMAIL> | testpass123 | Coming Soon |
| Investor | David Investor | <EMAIL> | testpass123 | Coming Soon |
| Renter | Lisa <PERSON> | <EMAIL> | testpass123 | Coming Soon |

### For Each User Type:

#### ✅ Registration Process
- [ ] Navigate to Sign Up screen
- [ ] Fill in Name field
- [ ] Fill in Email field
- [ ] Select correct User Type from dropdown
- [ ] Enter Password (minimum 6 characters)
- [ ] Enter Confirm Password (matching)
- [ ] Tap "Create Account" button
- [ ] Verify loading state is shown
- [ ] Verify successful registration (no error alerts)
- [ ] Verify automatic login after registration
- [ ] Verify redirect to dashboard

#### ✅ Firestore Document Creation
- [ ] Check Firebase Console > Firestore > users collection
- [ ] Verify user document exists with correct UID
- [ ] Verify document contains correct fields:
  - name
  - email
  - userType
  - createdAt
  - updatedAt
  - preferences object

#### ✅ Dashboard Content Verification
- [ ] Verify welcome message shows correct name
- [ ] Verify user type is displayed correctly
- [ ] Verify role-specific action buttons are shown
- [ ] Verify profile tab shows correct information

---

## 2. LOGIN TESTING

### For Each Previously Created User:

#### ✅ Login Process
- [ ] Logout if currently logged in
- [ ] Navigate to Login screen
- [ ] Enter correct email
- [ ] Enter correct password
- [ ] Tap "Sign In" button
- [ ] Verify loading state is shown
- [ ] Verify successful login (no error alerts)
- [ ] Verify redirect to dashboard
- [ ] Verify user data loads correctly

#### ✅ User Data Retrieval
- [ ] Check console logs for "Fetching user data for UID"
- [ ] Check console logs for "User data found"
- [ ] Verify no "User document does not exist" messages
- [ ] Verify no Firestore permission errors

---

## 3. PERSISTENCE TESTING

#### ✅ AsyncStorage Persistence
- [ ] Login with any test user
- [ ] Verify user is logged in and dashboard is shown
- [ ] Close app completely (force close from recent apps)
- [ ] Reopen the app
- [ ] Verify user remains logged in (no redirect to login screen)
- [ ] Verify user data is still available
- [ ] Verify dashboard content loads correctly

---

## 4. ERROR HANDLING TESTING

#### ✅ Invalid Credentials
- [ ] Try login with correct email, wrong password
- [ ] Verify error alert is shown with appropriate message
- [ ] Try login with non-existent email
- [ ] Verify error alert is shown

#### ✅ Validation Errors
- [ ] Try signup with empty name field
- [ ] Verify validation error is shown
- [ ] Try signup with empty email field
- [ ] Verify validation error is shown
- [ ] Try signup with password < 6 characters
- [ ] Verify validation error is shown
- [ ] Try signup with mismatched passwords
- [ ] Verify validation error is shown

#### ✅ Network Issues
- [ ] Turn off internet connection
- [ ] Try to login
- [ ] Verify appropriate error handling
- [ ] Try to signup
- [ ] Verify appropriate error handling
- [ ] Turn internet back on and retry

---

## 5. ROLE-BASED CONTENT VERIFICATION

### Buyer Dashboard
- [ ] User type displays as "Buyer"
- [ ] Shows "Search Properties" button
- [ ] Shows "View Favorites" button
- [ ] Shows "Contact Agents" button
- [ ] Shows "Save Searches" button

### Seller Dashboard
- [ ] User type displays as "Seller"
- [ ] Shows "Create Listing" button
- [ ] Shows "Manage Properties" button
- [ ] Shows "View Inquiries" button
- [ ] Shows "Analytics" button

### Agent Dashboard
- [ ] User type displays as "Agent"
- [ ] Shows "Create Listing" button
- [ ] Shows "Manage Listings" button
- [ ] Shows "CRM Dashboard" button
- [ ] Shows "Analytics" button

### Property Manager Dashboard
- [ ] User type displays as "Property Manager"
- [ ] Shows "Coming Soon" button

### Investor Dashboard
- [ ] User type displays as "Investor"
- [ ] Shows "Coming Soon" button

### Renter Dashboard
- [ ] User type displays as "Renter"
- [ ] Shows "Coming Soon" button

---

## 6. NAVIGATION TESTING

#### ✅ Tab Navigation
- [ ] Dashboard tab is accessible
- [ ] Search tab is accessible
- [ ] Profile tab is accessible
- [ ] Messages tab is accessible (if applicable)

#### ✅ Profile Screen
- [ ] Shows correct user name
- [ ] Shows correct email
- [ ] Shows correct user type
- [ ] Edit functionality works (if implemented)

---

## 7. LOGOUT TESTING

#### ✅ Logout Process
- [ ] Tap logout button from dashboard
- [ ] Verify confirmation dialog (if implemented)
- [ ] Confirm logout
- [ ] Verify redirect to login screen
- [ ] Verify user state is cleared
- [ ] Try to access protected screens
- [ ] Verify redirect to login screen

---

## 8. AUTOMATED TESTING (Optional)

#### ✅ Using AuthTester Component
- [ ] Navigate to `/test-auth` screen
- [ ] Run "Test All Sign Ups" button
- [ ] Verify all user types are created successfully
- [ ] Run "Test All Logins" button
- [ ] Verify all user types can login successfully
- [ ] Check test results for any failures

---

## TROUBLESHOOTING

### Common Issues and Solutions:

#### Firebase Permission Errors
- Ensure Firestore rules are deployed
- Check Firebase console for rule syntax errors
- Verify user is properly authenticated

#### AsyncStorage Warnings
- Ensure Firebase config uses `initializeAuth` with AsyncStorage
- Restart Metro bundler if needed

#### User Document Not Found
- Check if signup process completed successfully
- Verify Firestore document was created
- Check console logs for detailed error messages

#### Navigation Issues
- Verify AuthGuard is properly implemented
- Check if user state is properly managed
- Ensure proper route protection

---

## SUCCESS CRITERIA

✅ **All tests pass when:**
- All 6 user types can register successfully
- All 6 user types can login successfully
- Auth state persists after app restart
- Role-specific content displays correctly
- Error handling works appropriately
- Navigation functions properly
- No Firebase warnings in console
- No Firestore permission errors
