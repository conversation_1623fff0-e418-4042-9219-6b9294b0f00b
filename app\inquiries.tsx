import { useAuth } from '@/contexts/AuthContext';
import { Inquiry, InquiryService, Offer } from '@/services/inquiryService';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

export default function InquiriesScreen() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'inquiries' | 'offers'>('inquiries');
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
      
      // Subscribe to real-time updates
      const unsubscribeInquiries = InquiryService.subscribeToUserInquiries(
        user.id,
        'seller',
        (updatedInquiries) => {
          setInquiries(updatedInquiries);
        }
      );

      const unsubscribeOffers = InquiryService.subscribeToUserOffers(
        user.id,
        'seller',
        (updatedOffers) => {
          setOffers(updatedOffers);
        }
      );

      return () => {
        unsubscribeInquiries();
        unsubscribeOffers();
      };
    }
  }, [user]);

  const loadData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const [inquiriesResult, offersResult] = await Promise.all([
        InquiryService.getUserInquiries(user.id, 'seller'),
        InquiryService.getUserOffers(user.id, 'seller'),
      ]);

      if (inquiriesResult.success && inquiriesResult.data) {
        setInquiries(inquiriesResult.data);
      }

      if (offersResult.success && offersResult.data) {
        setOffers(offersResult.data);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptOffer = async (offer: Offer) => {
    Alert.alert(
      'Accept Offer',
      `Are you sure you want to accept the offer of ₹${offer.amount.toLocaleString()} from ${offer.buyerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          onPress: async () => {
            try {
              const result = await InquiryService.updateOfferStatus(offer.id, 'accepted');
              if (result.success) {
                Alert.alert('Success', 'Offer accepted! The buyer will be notified.');
              } else {
                Alert.alert('Error', 'Failed to accept offer');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to accept offer');
            }
          },
        },
      ]
    );
  };

  const handleRejectOffer = async (offer: Offer) => {
    Alert.alert(
      'Reject Offer',
      `Are you sure you want to reject the offer of ₹${offer.amount.toLocaleString()} from ${offer.buyerName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await InquiryService.updateOfferStatus(offer.id, 'rejected');
              if (result.success) {
                Alert.alert('Success', 'Offer rejected. The buyer will be notified.');
              } else {
                Alert.alert('Error', 'Failed to reject offer');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to reject offer');
            }
          },
        },
      ]
    );
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString([], {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#ffc107';
      case 'accepted':
        return '#28a745';
      case 'rejected':
        return '#dc3545';
      case 'countered':
        return '#007AFF';
      default:
        return '#6c757d';
    }
  };

  const renderInquiry = ({ item }: { item: Inquiry }) => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.propertyTitle}>{item.propertyTitle}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
        </View>
      </View>
      
      <Text style={styles.buyerName}>From: {item.buyerName}</Text>
      <Text style={styles.message}>{item.message}</Text>
      <Text style={styles.date}>{formatDate(item.createdAt)}</Text>

      {item.status === 'pending' && (
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.respondButton}
            onPress={() => {
              // Navigate to messaging
              Alert.alert('Respond', 'Messaging functionality will open here');
            }}
          >
            <Text style={styles.respondButtonText}>Respond</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const renderOffer = ({ item }: { item: Offer }) => {
    const isExpired = new Date() > item.expiresAt;
    
    return (
      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.propertyTitle}>{item.propertyTitle}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
            <Text style={styles.statusText}>{item.status.toUpperCase()}</Text>
          </View>
        </View>
        
        <Text style={styles.buyerName}>From: {item.buyerName}</Text>
        <View style={styles.offerDetails}>
          <Text style={styles.offerAmount}>Offer: ₹{item.amount.toLocaleString()}</Text>
          <Text style={styles.originalPrice}>Listed: ₹{item.originalPrice.toLocaleString()}</Text>
        </View>
        
        {item.message && <Text style={styles.message}>{item.message}</Text>}
        
        <View style={styles.offerFooter}>
          <Text style={styles.date}>{formatDate(item.createdAt)}</Text>
          {!isExpired && item.status === 'pending' && (
            <Text style={styles.expiresText}>
              Expires: {formatDate(item.expiresAt)}
            </Text>
          )}
          {isExpired && item.status === 'pending' && (
            <Text style={styles.expiredText}>EXPIRED</Text>
          )}
        </View>

        {item.status === 'pending' && !isExpired && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.rejectButton}
              onPress={() => handleRejectOffer(item)}
            >
              <Text style={styles.rejectButtonText}>Reject</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => handleAcceptOffer(item)}
            >
              <Text style={styles.acceptButtonText}>Accept</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyTitle}>
        No {activeTab === 'inquiries' ? 'Inquiries' : 'Offers'} Yet
      </Text>
      <Text style={styles.emptyText}>
        {activeTab === 'inquiries' 
          ? 'Property inquiries from interested buyers will appear here.'
          : 'Purchase offers from buyers will appear here.'
        }
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Inquiries & Offers</Text>
        <View style={{ width: 50 }} />
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'inquiries' && styles.activeTab]}
          onPress={() => setActiveTab('inquiries')}
        >
          <Text style={[styles.tabText, activeTab === 'inquiries' && styles.activeTabText]}>
            Inquiries ({inquiries.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'offers' && styles.activeTab]}
          onPress={() => setActiveTab('offers')}
        >
          <Text style={[styles.tabText, activeTab === 'offers' && styles.activeTabText]}>
            Offers ({offers.length})
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === 'inquiries' ? (
        <FlatList
          data={inquiries}
          renderItem={renderInquiry}
          keyExtractor={(item) => item.id}
          style={styles.list}
          contentContainerStyle={
            inquiries.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <FlatList
          data={offers}
          renderItem={renderOffer}
          keyExtractor={(item) => item.id}
          style={styles.list}
          contentContainerStyle={
            offers.length === 0 ? styles.emptyListContainer : styles.listContainer
          }
          ListEmptyComponent={renderEmptyState}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  backButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
    fontWeight: '600',
  },
  list: {
    flex: 1,
  },
  listContainer: {
    padding: 16,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  card: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  propertyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  buyerName: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  message: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
    marginBottom: 8,
  },
  date: {
    fontSize: 12,
    color: '#999',
  },
  offerDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  offerAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#28a745',
  },
  originalPrice: {
    fontSize: 14,
    color: '#666',
  },
  offerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  expiresText: {
    fontSize: 12,
    color: '#ffc107',
    fontWeight: '600',
  },
  expiredText: {
    fontSize: 12,
    color: '#dc3545',
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 12,
  },
  respondButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  respondButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  rejectButton: {
    flex: 1,
    backgroundColor: '#dc3545',
    paddingVertical: 8,
    borderRadius: 6,
    marginRight: 8,
  },
  rejectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  acceptButton: {
    flex: 1,
    backgroundColor: '#28a745',
    paddingVertical: 8,
    borderRadius: 6,
  },
  acceptButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});
