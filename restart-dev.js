// <PERSON>ript to restart development server with cache clearing
// Run this with: node restart-dev.js

const { exec } = require('child_process');

console.log('🔄 Restarting development server with cache clearing...');
console.log('This will fix TypeScript compilation and import issues.');

// Kill any existing Metro processes
console.log('1. Stopping existing Metro processes...');
exec('taskkill /f /im node.exe', (error) => {
  // Ignore errors as there might not be any running processes
  
  setTimeout(() => {
    console.log('2. Clearing Metro cache...');
    exec('npx expo start --clear', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Error starting development server:', error.message);
        console.log('\n📋 Manual steps:');
        console.log('1. Stop current Metro bundler (Ctrl+C)');
        console.log('2. Run: npx expo start --clear');
        console.log('3. Or run: npx react-native start --reset-cache');
        return;
      }
      
      if (stderr) {
        console.log('⚠️  Warnings:', stderr);
      }
      
      console.log('✅ Development server started successfully!');
      console.log(stdout);
    });
  }, 2000);
});

console.log('\n📝 What this fixes:');
console.log('- TypeScript compilation errors');
console.log('- Import/export resolution issues');
console.log('- JSX syntax errors');
console.log('- Metro bundler cache issues');

console.log('\n🎯 Expected results after restart:');
console.log('- No more "Property Shadows doesn\'t exist" errors');
console.log('- No more JSX closing tag errors');
console.log('- Clean TypeScript compilation');
console.log('- All modern UI components working');
