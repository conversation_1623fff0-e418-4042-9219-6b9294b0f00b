import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { InquiryService } from '@/services/inquiryService';

export default function CreateOfferScreen() {
  const { propertyId, propertyTitle, sellerId, sellerName, originalPrice } = useLocalSearchParams<{
    propertyId: string;
    propertyTitle: string;
    sellerId: string;
    sellerName: string;
    originalPrice: string;
  }>();

  const { user } = useAuth();
  const [offerAmount, setOfferAmount] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  const originalPriceNum = originalPrice ? Number(originalPrice) : 0;

  const calculateDifference = () => {
    const offer = Number(offerAmount);
    if (!offer || !originalPriceNum) return null;
    
    const difference = originalPriceNum - offer;
    const percentage = ((difference / originalPriceNum) * 100).toFixed(1);
    
    return {
      amount: difference,
      percentage: parseFloat(percentage),
    };
  };

  const handleSubmitOffer = async () => {
    if (!offerAmount.trim() || isNaN(Number(offerAmount))) {
      Alert.alert('Error', 'Please enter a valid offer amount');
      return;
    }

    const offer = Number(offerAmount);
    if (offer <= 0) {
      Alert.alert('Error', 'Offer amount must be greater than 0');
      return;
    }

    if (!user || !propertyId || !sellerId) {
      Alert.alert('Error', 'Missing required information');
      return;
    }

    setLoading(true);
    try {
      const result = await InquiryService.createOffer(
        propertyId,
        propertyTitle || 'Property',
        user.id,
        user.name,
        sellerId,
        sellerName || 'Property Owner',
        offer,
        originalPriceNum,
        message.trim() || undefined
      );

      if (result.success) {
        Alert.alert(
          'Success',
          'Your offer has been submitted to the property owner. They will review it and respond within 7 days.',
          [
            {
              text: 'OK',
              onPress: () => router.back(),
            },
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to submit offer');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to submit offer');
    } finally {
      setLoading(false);
    }
  };

  const difference = calculateDifference();

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Make Offer</Text>
        <View style={{ width: 50 }} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.propertyInfo}>
          <Text style={styles.propertyTitle}>{propertyTitle}</Text>
          <Text style={styles.sellerInfo}>To: {sellerName}</Text>
          <Text style={styles.originalPrice}>
            Listed Price: ₹{originalPriceNum.toLocaleString()}
          </Text>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Your Offer</Text>
          <Text style={styles.description}>
            Submit a competitive offer for this property. The seller will review and respond within 7 days.
          </Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Offer Amount (₹) *</Text>
            <TextInput
              style={styles.input}
              value={offerAmount}
              onChangeText={setOfferAmount}
              placeholder="Enter your offer amount"
              keyboardType="numeric"
            />
            {difference && (
              <View style={styles.differenceContainer}>
                <Text style={[
                  styles.differenceText,
                  difference.amount > 0 ? styles.lowerOffer : styles.higherOffer
                ]}>
                  {difference.amount > 0 
                    ? `₹${difference.amount.toLocaleString()} (${difference.percentage}%) below asking price`
                    : `₹${Math.abs(difference.amount).toLocaleString()} (${Math.abs(difference.percentage)}%) above asking price`
                  }
                </Text>
              </View>
            )}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Message (Optional)</Text>
            <TextInput
              style={styles.textArea}
              value={message}
              onChangeText={setMessage}
              placeholder="Add a personal message to strengthen your offer..."
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              maxLength={500}
            />
            <Text style={styles.characterCount}>{message.length}/500</Text>
          </View>

          <View style={styles.tipContainer}>
            <Text style={styles.tipTitle}>💡 Tips for a strong offer:</Text>
            <Text style={styles.tipText}>• Research comparable properties in the area</Text>
            <Text style={styles.tipText}>• Consider market conditions</Text>
            <Text style={styles.tipText}>• Be prepared to move quickly if accepted</Text>
            <Text style={styles.tipText}>• Include financing pre-approval if applicable</Text>
          </View>

          <View style={styles.warningContainer}>
            <Text style={styles.warningTitle}>⚠️ Important:</Text>
            <Text style={styles.warningText}>
              This offer is legally binding if accepted. Make sure you're ready to proceed with the purchase at the offered amount.
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmitOffer}
            disabled={loading || !offerAmount.trim()}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Submitting...' : 'Submit Offer'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  backButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  propertyInfo: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  propertyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  sellerInfo: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  originalPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  formSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    height: 80,
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 4,
  },
  differenceContainer: {
    marginTop: 8,
  },
  differenceText: {
    fontSize: 14,
    fontWeight: '600',
  },
  lowerOffer: {
    color: '#ff6b6b',
  },
  higherOffer: {
    color: '#51cf66',
  },
  tipContainer: {
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  tipTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  warningContainer: {
    backgroundColor: '#fff3cd',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 8,
  },
  warningText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  submitButton: {
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  submitButtonDisabled: {
    backgroundColor: '#ccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
