import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { UserType } from '@/types';

interface TestUser {
  userType: UserType;
  name: string;
  email: string;
  password: string;
  expectedFeatures: string[];
}

const testUsers: TestUser[] = [
  {
    userType: 'buyer',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Search Properties', 'View Favorites', 'Contact Agents']
  },
  {
    userType: 'seller',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Create Listing', 'Manage Properties', 'View Inquiries']
  },
  {
    userType: 'agent',
    name: 'Mike <PERSON>',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Create Listing', 'Manage Listings', 'CRM Dashboard', 'Analytics']
  },
  {
    userType: 'property_manager',
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Coming Soon']
  },
  {
    userType: 'investor',
    name: 'David Investor',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Coming Soon']
  },
  {
    userType: 'renter',
    name: 'Lisa Renter',
    email: '<EMAIL>',
    password: 'testpass123',
    expectedFeatures: ['Coming Soon']
  }
];

export const AuthTester: React.FC = () => {
  const { signUp, signIn, logout, user, loading } = useAuth();
  const [testResults, setTestResults] = useState<{ [key: string]: string }>({});
  const [currentTest, setCurrentTest] = useState<string | null>(null);

  const addResult = (testName: string, result: string) => {
    setTestResults(prev => ({ ...prev, [testName]: result }));
  };

  const testSignUp = async (testUser: TestUser) => {
    const testName = `SignUp-${testUser.userType}`;
    setCurrentTest(testName);
    
    try {
      await signUp(testUser.email, testUser.password, testUser.name, testUser.userType);
      addResult(testName, '✅ SUCCESS');
      Alert.alert('Success', `${testUser.userType} account created successfully!`);
    } catch (error: any) {
      addResult(testName, `❌ FAILED: ${error.message}`);
      Alert.alert('Error', `Failed to create ${testUser.userType} account: ${error.message}`);
    } finally {
      setCurrentTest(null);
    }
  };

  const testLogin = async (testUser: TestUser) => {
    const testName = `Login-${testUser.userType}`;
    setCurrentTest(testName);
    
    try {
      await signIn(testUser.email, testUser.password);
      addResult(testName, '✅ SUCCESS');
      Alert.alert('Success', `${testUser.userType} login successful!`);
    } catch (error: any) {
      addResult(testName, `❌ FAILED: ${error.message}`);
      Alert.alert('Error', `Failed to login ${testUser.userType}: ${error.message}`);
    } finally {
      setCurrentTest(null);
    }
  };

  const testLogout = async () => {
    const testName = 'Logout';
    setCurrentTest(testName);
    
    try {
      await logout();
      addResult(testName, '✅ SUCCESS');
      Alert.alert('Success', 'Logout successful!');
    } catch (error: any) {
      addResult(testName, `❌ FAILED: ${error.message}`);
      Alert.alert('Error', `Logout failed: ${error.message}`);
    } finally {
      setCurrentTest(null);
    }
  };

  const runAllSignUpTests = async () => {
    for (const testUser of testUsers) {
      await testSignUp(testUser);
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const runAllLoginTests = async () => {
    for (const testUser of testUsers) {
      if (user) await logout(); // Logout first
      await new Promise(resolve => setTimeout(resolve, 500));
      await testLogin(testUser);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  };

  const clearResults = () => {
    setTestResults({});
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Authentication Tester</Text>
      
      {user && (
        <View style={styles.currentUser}>
          <Text style={styles.currentUserText}>
            Current User: {user.name} ({user.userType})
          </Text>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Batch Tests</Text>
        <TouchableOpacity 
          style={styles.button} 
          onPress={runAllSignUpTests}
          disabled={loading || currentTest !== null}
        >
          <Text style={styles.buttonText}>Test All Sign Ups</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={runAllLoginTests}
          disabled={loading || currentTest !== null}
        >
          <Text style={styles.buttonText}>Test All Logins</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.button} 
          onPress={testLogout}
          disabled={loading || currentTest !== null || !user}
        >
          <Text style={styles.buttonText}>Test Logout</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Individual Tests</Text>
        {testUsers.map((testUser, index) => (
          <View key={index} style={styles.userTestContainer}>
            <Text style={styles.userTypeText}>{testUser.userType.toUpperCase()}</Text>
            <View style={styles.userTestButtons}>
              <TouchableOpacity 
                style={styles.smallButton} 
                onPress={() => testSignUp(testUser)}
                disabled={loading || currentTest !== null}
              >
                <Text style={styles.smallButtonText}>Sign Up</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={styles.smallButton} 
                onPress={() => testLogin(testUser)}
                disabled={loading || currentTest !== null}
              >
                <Text style={styles.smallButtonText}>Login</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </View>

      {currentTest && (
        <View style={styles.currentTestContainer}>
          <ActivityIndicator size="small" color="#007AFF" />
          <Text style={styles.currentTestText}>Running: {currentTest}</Text>
        </View>
      )}

      <View style={styles.section}>
        <View style={styles.resultsHeader}>
          <Text style={styles.sectionTitle}>Test Results</Text>
          <TouchableOpacity style={styles.clearButton} onPress={clearResults}>
            <Text style={styles.clearButtonText}>Clear</Text>
          </TouchableOpacity>
        </View>
        {Object.entries(testResults).map(([testName, result]) => (
          <View key={testName} style={styles.resultItem}>
            <Text style={styles.resultText}>{testName}: {result}</Text>
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  currentUser: {
    backgroundColor: '#e3f2fd',
    padding: 10,
    borderRadius: 8,
    marginBottom: 20,
  },
  currentUserText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  buttonText: {
    color: 'white',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
  },
  userTestContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  userTypeText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  userTestButtons: {
    flexDirection: 'row',
    gap: 10,
  },
  smallButton: {
    backgroundColor: '#34C759',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  smallButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  currentTestContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff3cd',
    padding: 10,
    borderRadius: 8,
    marginBottom: 20,
  },
  currentTestText: {
    marginLeft: 10,
    fontSize: 16,
    fontWeight: '500',
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  clearButton: {
    backgroundColor: '#FF3B30',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  resultItem: {
    backgroundColor: 'white',
    padding: 10,
    borderRadius: 6,
    marginBottom: 5,
  },
  resultText: {
    fontSize: 14,
    fontFamily: 'monospace',
  },
});
