{"name": "real-estate", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-chart-kit", "react-native-document-picker", "firebase", "react-native-elements", "react-native-vector-icons"], "listUnknownPackages": false}}}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.4.5", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "firebase": "^12.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-calendars": "^1.1313.0", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.13", "react-native-document-picker": "^9.3.1", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-maps": "1.20.1", "react-native-modal": "^14.0.0-rc.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.2.0", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}