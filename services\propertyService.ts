import {
  collection,
  addDoc,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  QueryDocumentSnapshot,
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '@/config/firebase';
import { Property, PropertyFilters, ApiResponse } from '@/types';

export class PropertyService {
  private static readonly COLLECTION_NAME = 'properties';

  // Create a new property listing
  static async createProperty(propertyData: Omit<Property, 'id' | 'createdAt' | 'updatedAt' | 'views' | 'inquiries'>): Promise<ApiResponse<Property>> {
    try {
      const now = new Date();
      const propertyWithTimestamps = {
        ...propertyData,
        createdAt: now,
        updatedAt: now,
        views: 0,
        inquiries: 0,
      };

      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), propertyWithTimestamps);
      
      const createdProperty: Property = {
        ...propertyWithTimestamps,
        id: docRef.id,
      };

      return {
        success: true,
        data: createdProperty,
        message: 'Property created successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Upload property images
  static async uploadPropertyImages(propertyId: string, imageUris: string[]): Promise<ApiResponse<string[]>> {
    try {
      const uploadPromises = imageUris.map(async (uri, index) => {
        const response = await fetch(uri);
        const blob = await response.blob();
        const imageName = `property_${propertyId}_${index}_${Date.now()}.jpg`;
        const imageRef = ref(storage, `properties/${propertyId}/images/${imageName}`);
        
        await uploadBytes(imageRef, blob);
        return await getDownloadURL(imageRef);
      });

      const downloadUrls = await Promise.all(uploadPromises);

      return {
        success: true,
        data: downloadUrls,
        message: 'Images uploaded successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Upload legal documents
  static async uploadLegalDocument(propertyId: string, documentUri: string, documentType: string): Promise<ApiResponse<string>> {
    try {
      const response = await fetch(documentUri);
      const blob = await response.blob();
      const documentName = `${documentType}_${Date.now()}.pdf`;
      const documentRef = ref(storage, `properties/${propertyId}/documents/${documentName}`);
      
      await uploadBytes(documentRef, blob);
      const downloadUrl = await getDownloadURL(documentRef);

      return {
        success: true,
        data: downloadUrl,
        message: 'Document uploaded successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Get property by ID
  static async getPropertyById(propertyId: string): Promise<ApiResponse<Property>> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, propertyId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const property: Property = {
          id: docSnap.id,
          ...docSnap.data(),
          createdAt: docSnap.data().createdAt?.toDate() || new Date(),
          updatedAt: docSnap.data().updatedAt?.toDate() || new Date(),
        } as Property;

        return {
          success: true,
          data: property,
        };
      } else {
        return {
          success: false,
          error: 'Property not found',
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Search properties with filters
  static async searchProperties(filters: PropertyFilters, lastDoc?: QueryDocumentSnapshot, limitCount: number = 10): Promise<ApiResponse<{ properties: Property[]; lastDoc?: QueryDocumentSnapshot }>> {
    try {
      let q = query(collection(db, this.COLLECTION_NAME));

      // Apply filters
      if (filters.propertyType && filters.propertyType.length > 0) {
        q = query(q, where('propertyType', 'in', filters.propertyType));
      }

      if (filters.location) {
        q = query(q, where('location.city', '>=', filters.location), where('location.city', '<=', filters.location + '\uf8ff'));
      }

      if (filters.priceRange) {
        if (filters.priceRange.min) {
          q = query(q, where('price', '>=', filters.priceRange.min));
        }
        if (filters.priceRange.max) {
          q = query(q, where('price', '<=', filters.priceRange.max));
        }
      }

      if (filters.reraCompliant) {
        q = query(q, where('reraDetails.verified', '==', true));
      }

      // Add ordering and pagination
      q = query(q, orderBy('createdAt', 'desc'), limit(limitCount));

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const properties: Property[] = [];
      let newLastDoc: QueryDocumentSnapshot | undefined;

      querySnapshot.forEach((doc) => {
        properties.push({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        } as Property);
        newLastDoc = doc;
      });

      return {
        success: true,
        data: {
          properties,
          lastDoc: newLastDoc,
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Get properties by owner
  static async getPropertiesByOwner(ownerId: string): Promise<ApiResponse<Property[]>> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('ownerId', '==', ownerId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const properties: Property[] = [];

      querySnapshot.forEach((doc) => {
        properties.push({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate() || new Date(),
          updatedAt: doc.data().updatedAt?.toDate() || new Date(),
        } as Property);
      });

      return {
        success: true,
        data: properties,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Update property
  static async updateProperty(propertyId: string, updates: Partial<Property>): Promise<ApiResponse<void>> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, propertyId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: new Date(),
      });

      return {
        success: true,
        message: 'Property updated successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Delete property
  static async deleteProperty(propertyId: string): Promise<ApiResponse<void>> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, propertyId);
      await deleteDoc(docRef);

      return {
        success: true,
        message: 'Property deleted successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Increment property views
  static async incrementViews(propertyId: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, propertyId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const currentViews = docSnap.data().views || 0;
        await updateDoc(docRef, {
          views: currentViews + 1,
        });
      }
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  }
}
