// Firebase CLI Setup and Deployment Script
// Run this with: node setup-firebase-cli.js

const { exec } = require('child_process');
const fs = require('fs');

console.log('🔥 Firebase CLI Setup and Deployment Script');
console.log('==========================================\n');

// Check if Firebase CLI is installed
function checkFirebaseCLI() {
  return new Promise((resolve) => {
    exec('firebase --version', (error, stdout, stderr) => {
      if (error) {
        console.log('❌ Firebase CLI not found');
        resolve(false);
      } else {
        console.log('✅ Firebase CLI found:', stdout.trim());
        resolve(true);
      }
    });
  });
}

// Install Firebase CLI
function installFirebaseCLI() {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing Firebase CLI...');
    exec('npm install -g firebase-tools', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Failed to install Firebase CLI:', error.message);
        reject(error);
      } else {
        console.log('✅ Firebase CLI installed successfully');
        resolve();
      }
    });
  });
}

// Check if user is logged in
function checkFirebaseLogin() {
  return new Promise((resolve) => {
    exec('firebase projects:list', (error, stdout, stderr) => {
      if (error || stderr.includes('not authenticated')) {
        console.log('❌ Not logged in to Firebase');
        resolve(false);
      } else {
        console.log('✅ Logged in to Firebase');
        resolve(true);
      }
    });
  });
}

// Deploy Firestore rules
function deployFirestoreRules() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Deploying Firestore rules...');
    exec('firebase deploy --only firestore:rules', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Failed to deploy rules:', error.message);
        reject(error);
      } else {
        console.log('✅ Firestore rules deployed successfully');
        console.log(stdout);
        resolve();
      }
    });
  });
}

// Deploy Firestore indexes
function deployFirestoreIndexes() {
  return new Promise((resolve, reject) => {
    console.log('📊 Deploying Firestore indexes...');
    exec('firebase deploy --only firestore:indexes', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Failed to deploy indexes:', error.message);
        reject(error);
      } else {
        console.log('✅ Firestore indexes deployed successfully');
        console.log(stdout);
        resolve();
      }
    });
  });
}

// Main execution
async function main() {
  try {
    // Step 1: Check if Firebase CLI is installed
    const cliInstalled = await checkFirebaseCLI();
    
    if (!cliInstalled) {
      console.log('\n🔧 Installing Firebase CLI...');
      await installFirebaseCLI();
    }

    // Step 2: Check if logged in
    const loggedIn = await checkFirebaseLogin();
    
    if (!loggedIn) {
      console.log('\n🔑 Please login to Firebase:');
      console.log('Run: firebase login');
      console.log('Then run this script again.');
      return;
    }

    // Step 3: Check if firebase.json exists
    if (!fs.existsSync('firebase.json')) {
      console.log('\n⚠️  firebase.json not found');
      console.log('Please run: firebase init');
      console.log('Select Firestore and follow the setup wizard.');
      return;
    }

    // Step 4: Deploy rules
    console.log('\n📋 Deploying Firestore rules...');
    await deployFirestoreRules();

    // Step 5: Deploy indexes
    console.log('\n📊 Deploying Firestore indexes...');
    await deployFirestoreIndexes();

    console.log('\n🎉 All Firebase deployments completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Clear Metro cache: npx expo start --clear');
    console.log('2. Test your authentication system');
    console.log('3. Check for any remaining errors');

  } catch (error) {
    console.error('\n💥 Error during setup:', error.message);
    console.log('\nManual steps:');
    console.log('1. Install Firebase CLI: npm install -g firebase-tools');
    console.log('2. Login: firebase login');
    console.log('3. Initialize: firebase init');
    console.log('4. Deploy rules: firebase deploy --only firestore:rules');
    console.log('5. Deploy indexes: firebase deploy --only firestore:indexes');
  }
}

// Run the script
main();
