/**
 * Modern Real Estate App Design System
 * Professional color palette optimized for real estate applications
 */

// Primary brand colors - Professional blue-green palette
const primaryLight = '#2563EB'; // Modern blue
const primaryDark = '#3B82F6';
const secondaryLight = '#059669'; // Success green
const secondaryDark = '#10B981';

// Neutral colors - Warm grays for better readability
const neutrals = {
  50: '#FAFAF9',
  100: '#F5F5F4',
  200: '#E7E5E4',
  300: '#D6D3D1',
  400: '#A8A29E',
  500: '#78716C',
  600: '#57534E',
  700: '#44403C',
  800: '#292524',
  900: '#1C1917',
};

// Semantic colors
const semantic = {
  success: '#059669',
  warning: '#D97706',
  error: '#DC2626',
  info: '#2563EB',
};

export const Colors = {
  light: {
    // Primary colors
    primary: primaryLight,
    primaryLight: '#3B82F6',
    primaryDark: '#1D4ED8',
    secondary: secondaryLight,

    // Text colors
    text: neutrals[900],
    textSecondary: neutrals[600],
    textMuted: neutrals[500],
    textInverse: '#FFFFFF',

    // Background colors
    background: '#FFFFFF',
    backgroundSecondary: neutrals[50],
    backgroundMuted: neutrals[100],

    // Surface colors
    surface: '#FFFFFF',
    surfaceElevated: '#FFFFFF',

    // Border colors
    border: neutrals[200],
    borderLight: neutrals[100],
    borderFocus: primaryLight,

    // Tab bar
    tint: primaryLight,
    tabIconDefault: neutrals[500],
    tabIconSelected: primaryLight,
    tabBackground: '#FFFFFF',

    // Semantic colors
    success: semantic.success,
    warning: semantic.warning,
    error: semantic.error,
    info: semantic.info,

    // Card and component colors
    cardBackground: '#FFFFFF',
    cardShadow: 'rgba(0, 0, 0, 0.08)',
    overlay: 'rgba(0, 0, 0, 0.5)',

    // Input colors
    inputBackground: '#FFFFFF',
    inputBorder: neutrals[300],
    inputFocus: primaryLight,
    inputPlaceholder: neutrals[400],
  },
  dark: {
    // Primary colors
    primary: primaryDark,
    primaryLight: '#60A5FA',
    primaryDark: '#1E40AF',
    secondary: secondaryDark,

    // Text colors
    text: neutrals[50],
    textSecondary: neutrals[300],
    textMuted: neutrals[400],
    textInverse: neutrals[900],

    // Background colors
    background: neutrals[900],
    backgroundSecondary: neutrals[800],
    backgroundMuted: neutrals[700],

    // Surface colors
    surface: neutrals[800],
    surfaceElevated: neutrals[700],

    // Border colors
    border: neutrals[600],
    borderLight: neutrals[700],
    borderFocus: primaryDark,

    // Tab bar
    tint: primaryDark,
    tabIconDefault: neutrals[400],
    tabIconSelected: primaryDark,
    tabBackground: neutrals[800],

    // Semantic colors
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    // Card and component colors
    cardBackground: neutrals[800],
    cardShadow: 'rgba(0, 0, 0, 0.3)',
    overlay: 'rgba(0, 0, 0, 0.7)',

    // Input colors
    inputBackground: neutrals[700],
    inputBorder: neutrals[600],
    inputFocus: primaryDark,
    inputPlaceholder: neutrals[400],
  },
};
