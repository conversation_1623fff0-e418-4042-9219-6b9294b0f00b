/**
 * Modern Design System for Real Estate App
 * Comprehensive design tokens for consistent UI/UX
 */

import { StyleSheet } from 'react-native';
import { Colors } from './Colors';

// Typography Scale
export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },
  
  // Font weights
  fontWeight: {
    light: '300' as const,
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
    extrabold: '800' as const,
  },
  
  // Line heights
  lineHeight: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

// Spacing Scale (based on 4px grid)
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 48,
  '4xl': 64,
  '5xl': 80,
  '6xl': 96,
};

// Border Radius
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

// Shadows
export const Shadows = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 6,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.18,
    shadowRadius: 24,
    elevation: 8,
  },
};

// Common component styles
export const createStyles = (colorScheme: 'light' | 'dark') => {
  const colors = Colors[colorScheme];
  
  return StyleSheet.create({
    // Container styles
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    
    safeContainer: {
      flex: 1,
      backgroundColor: colors.background,
      paddingTop: 44, // Safe area top
    },
    
    // Card styles
    card: {
      backgroundColor: colors.cardBackground,
      borderRadius: BorderRadius.lg,
      padding: Spacing.base,
      marginBottom: Spacing.base,
      ...Shadows.base,
    },
    
    cardElevated: {
      backgroundColor: colors.surfaceElevated,
      borderRadius: BorderRadius.lg,
      padding: Spacing.lg,
      marginBottom: Spacing.base,
      ...Shadows.md,
    },
    
    // Button styles
    buttonPrimary: {
      backgroundColor: colors.primary,
      borderRadius: BorderRadius.md,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
      ...Shadows.sm,
    },
    
    buttonSecondary: {
      backgroundColor: 'transparent',
      borderWidth: 1.5,
      borderColor: colors.primary,
      borderRadius: BorderRadius.md,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.xl,
      alignItems: 'center',
      justifyContent: 'center',
    },
    
    buttonText: {
      color: colors.textInverse,
      fontSize: Typography.fontSize.base,
      fontWeight: Typography.fontWeight.semibold,
    },
    
    buttonTextSecondary: {
      color: colors.primary,
      fontSize: Typography.fontSize.base,
      fontWeight: Typography.fontWeight.semibold,
    },
    
    // Input styles
    input: {
      backgroundColor: colors.inputBackground,
      borderWidth: 1,
      borderColor: colors.inputBorder,
      borderRadius: BorderRadius.md,
      paddingVertical: Spacing.md,
      paddingHorizontal: Spacing.base,
      fontSize: Typography.fontSize.base,
      color: colors.text,
    },
    
    inputFocused: {
      borderColor: colors.inputFocus,
      ...Shadows.sm,
    },
    
    // Text styles
    heading1: {
      fontSize: Typography.fontSize['4xl'],
      fontWeight: Typography.fontWeight.bold,
      color: colors.text,
      lineHeight: Typography.fontSize['4xl'] * Typography.lineHeight.tight,
    },
    
    heading2: {
      fontSize: Typography.fontSize['3xl'],
      fontWeight: Typography.fontWeight.bold,
      color: colors.text,
      lineHeight: Typography.fontSize['3xl'] * Typography.lineHeight.tight,
    },
    
    heading3: {
      fontSize: Typography.fontSize['2xl'],
      fontWeight: Typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: Typography.fontSize['2xl'] * Typography.lineHeight.tight,
    },
    
    heading4: {
      fontSize: Typography.fontSize.xl,
      fontWeight: Typography.fontWeight.semibold,
      color: colors.text,
      lineHeight: Typography.fontSize.xl * Typography.lineHeight.normal,
    },
    
    bodyLarge: {
      fontSize: Typography.fontSize.lg,
      fontWeight: Typography.fontWeight.normal,
      color: colors.text,
      lineHeight: Typography.fontSize.lg * Typography.lineHeight.normal,
    },
    
    body: {
      fontSize: Typography.fontSize.base,
      fontWeight: Typography.fontWeight.normal,
      color: colors.text,
      lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    },
    
    bodySmall: {
      fontSize: Typography.fontSize.sm,
      fontWeight: Typography.fontWeight.normal,
      color: colors.textSecondary,
      lineHeight: Typography.fontSize.sm * Typography.lineHeight.normal,
    },
    
    caption: {
      fontSize: Typography.fontSize.xs,
      fontWeight: Typography.fontWeight.normal,
      color: colors.textMuted,
      lineHeight: Typography.fontSize.xs * Typography.lineHeight.normal,
    },
    
    // Layout styles
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    
    rowBetween: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    
    column: {
      flexDirection: 'column',
    },
    
    center: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    
    // Spacing utilities
    mt: (size: keyof typeof Spacing) => ({ marginTop: Spacing[size] }),
    mb: (size: keyof typeof Spacing) => ({ marginBottom: Spacing[size] }),
    ml: (size: keyof typeof Spacing) => ({ marginLeft: Spacing[size] }),
    mr: (size: keyof typeof Spacing) => ({ marginRight: Spacing[size] }),
    mx: (size: keyof typeof Spacing) => ({ 
      marginLeft: Spacing[size], 
      marginRight: Spacing[size] 
    }),
    my: (size: keyof typeof Spacing) => ({ 
      marginTop: Spacing[size], 
      marginBottom: Spacing[size] 
    }),
    
    pt: (size: keyof typeof Spacing) => ({ paddingTop: Spacing[size] }),
    pb: (size: keyof typeof Spacing) => ({ paddingBottom: Spacing[size] }),
    pl: (size: keyof typeof Spacing) => ({ paddingLeft: Spacing[size] }),
    pr: (size: keyof typeof Spacing) => ({ paddingRight: Spacing[size] }),
    px: (size: keyof typeof Spacing) => ({ 
      paddingLeft: Spacing[size], 
      paddingRight: Spacing[size] 
    }),
    py: (size: keyof typeof Spacing) => ({ 
      paddingTop: Spacing[size], 
      paddingBottom: Spacing[size] 
    }),
  });
};
