import { useAuth } from '@/contexts/AuthContext';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function DashboardScreen() {
  const { user, logout } = useAuth();

  const renderDashboardContent = () => {
    if (!user) return null;

    const userTypeDisplayName = {
      buyer: 'Buyer',
      seller: 'Seller',
      agent: 'Agent',
      property_manager: 'Property Manager',
      investor: 'Investor',
      renter: 'Renter',
    };

    return (
      <View style={styles.dashboardContent}>
        <View style={styles.userTypeCard}>
          <Text style={styles.userTypeLabel}>Account Type</Text>
          <Text style={styles.userTypeValue}>
            {userTypeDisplayName[user.userType] || user.userType}
          </Text>
        </View>

        <View style={styles.quickActions}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>

          {user.userType === 'buyer' && (
            <>
              <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/(tabs)/search')}>
                <Text style={styles.actionButtonText}>Search Properties</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/favorites')}>
                <Text style={styles.actionButtonText}>View Saved Properties</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>Mortgage Calculator</Text>
              </TouchableOpacity>
            </>
          )}

          {user.userType === 'seller' && (
            <>
              <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/property/create')}>
                <Text style={styles.actionButtonText}>Create New Listing</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>Manage Listings</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/inquiries')}>
                <Text style={styles.actionButtonText}>View Inquiries</Text>
              </TouchableOpacity>
            </>
          )}

          {user.userType === 'agent' && (
            <>
              <TouchableOpacity style={styles.actionButton} onPress={() => router.push('/property/create')}>
                <Text style={styles.actionButtonText}>Create Listing</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>Manage Listings</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>CRM Dashboard</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>Analytics</Text>
              </TouchableOpacity>
            </>
          )}

          {(user.userType === 'property_manager' || user.userType === 'investor' || user.userType === 'renter') && (
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonText}>Coming Soon</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Overview</Text>
          <Text style={styles.statsText}>
            Welcome to your Real Estate dashboard. More features will be available soon.
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Welcome back,</Text>
          <Text style={styles.userName}>{user?.name}</Text>
        </View>
        <TouchableOpacity style={styles.logoutButton} onPress={logout}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {renderDashboardContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  greeting: {
    fontSize: 16,
    color: '#666',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  logoutButton: {
    backgroundColor: '#ff4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  logoutText: {
    color: '#fff',
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  dashboardContent: {
    padding: 20,
  },
  userTypeCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userTypeLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  userTypeValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  quickActions: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  actionButton: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  actionButtonText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
    textAlign: 'center',
  },
  statsSection: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
});
