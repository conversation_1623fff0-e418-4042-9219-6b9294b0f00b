import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Shadows, Spacing, Typography } from '@/constants/Design';
import { useAuth } from '@/contexts/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function DashboardScreen() {
  const { user, logout } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const renderDashboardContent = () => {
    if (!user) return null;

    const userTypeDisplayName = {
      buyer: 'Buyer',
      seller: 'Seller',
      agent: 'Agent',
      property_manager: 'Property Manager',
      investor: 'Investor',
      renter: 'Renter',
    };

    return (
      <View style={styles.dashboardContent}>
        {/* Welcome Header */}
        <Card style={{ marginBottom: Spacing.lg }}>
          <CardHeader>
            <Text style={[styles.welcomeTitle, { color: colors.text }]}>
              Welcome back, {user.name}!
            </Text>
            <Text style={[styles.welcomeSubtitle, { color: colors.textSecondary }]}>
              {userTypeDisplayName[user.userType] || user.userType} Account
            </Text>
          </CardHeader>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Quick Actions</Text>
          </CardHeader>
          <CardContent>

            {user.userType === 'buyer' && (
              <View style={styles.actionGrid}>
                <Button
                  title="Search Properties"
                  onPress={() => router.push('/(tabs)/search')}
                  variant="primary"
                  style={styles.actionButton}
                  icon={<IconSymbol name="magnifyingglass" size={20} color="white" />}
                />
                <Button
                  title="View Favorites"
                  onPress={() => router.push('/favorites')}
                  variant="outline"
                  style={styles.actionButton}
                  icon={<IconSymbol name="heart.fill" size={20} color={colors.primary} />}
                />
                <Button
                  title="Mortgage Calculator"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
              </View>
            )}

            {user.userType === 'seller' && (
              <View style={styles.actionGrid}>
                <Button
                  title="Create New Listing"
                  onPress={() => router.push('/property/create')}
                  variant="primary"
                  style={styles.actionButton}
                  icon={<IconSymbol name="plus" size={20} color="white" />}
                />
                <Button
                  title="Manage Listings"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
                <Button
                  title="View Inquiries"
                  onPress={() => router.push('/inquiries')}
                  variant="outline"
                  style={styles.actionButton}
                />
              </View>
            )}

            {user.userType === 'agent' && (
              <View style={styles.actionGrid}>
                <Button
                  title="Create Listing"
                  onPress={() => router.push('/property/create')}
                  variant="primary"
                  style={styles.actionButton}
                  icon={<IconSymbol name="plus" size={20} color="white" />}
                />
                <Button
                  title="Manage Listings"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
                <Button
                  title="CRM Dashboard"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
                <Button
                  title="Analytics"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
              </View>
            )}

            {(user.userType === 'property_manager' || user.userType === 'investor' || user.userType === 'renter') && (
              <View style={styles.actionGrid}>
                <Button
                  title="Coming Soon"
                  onPress={() => {}}
                  variant="outline"
                  style={styles.actionButton}
                />
              </View>
            )}
          </CardContent>
        </Card>

        {/* Overview Section */}
        <Card style={{ marginTop: Spacing.lg }}>
          <CardHeader>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Overview</Text>
          </CardHeader>
          <CardContent>
            <Text style={[styles.statsText, { color: colors.textSecondary }]}>
              Welcome to your Real Estate dashboard. More features will be available soon.
            </Text>
          </CardContent>
        </Card>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Welcome back,</Text>
          <Text style={styles.userName}>{user?.name}</Text>
        </View>
        <TouchableOpacity style={styles.logoutButton} onPress={logout}>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {renderDashboardContent()}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.lg,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    paddingTop: 50,
    ...Shadows.sm,
  },
  greeting: {
    fontSize: Typography.fontSize.base,
    color: '#64748b',
    fontWeight: Typography.fontWeight.normal,
  },
  userName: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: '#1e293b',
  },

  // Welcome section styles
  welcomeTitle: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Spacing.xs,
  },
  welcomeSubtitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },

  logoutButton: {
    backgroundColor: '#ef4444',
    paddingHorizontal: Spacing.base,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.md,
    ...Shadows.sm,
  },
  logoutText: {
    color: '#ffffff',
    fontWeight: Typography.fontWeight.semibold,
    fontSize: Typography.fontSize.sm,
  },
  content: {
    flex: 1,
  },
  dashboardContent: {
    padding: Spacing.lg,
    backgroundColor: '#f8fafc',
  },

  // Action grid for modern button layout
  actionGrid: {
    gap: Spacing.md,
  },

  sectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Spacing.base,
  },

  actionButton: {
    marginBottom: Spacing.sm,
  },
  statsSection: {
    backgroundColor: '#ffffff',
    padding: Spacing.lg,
    borderRadius: BorderRadius.lg,
    ...Shadows.base,
  },
  statsText: {
    fontSize: Typography.fontSize.base,
    color: '#64748b',
    lineHeight: Typography.fontSize.base * Typography.lineHeight.normal,
    fontWeight: Typography.fontWeight.normal,
  },
});
