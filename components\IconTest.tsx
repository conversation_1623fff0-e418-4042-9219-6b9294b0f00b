import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { IconSymbol } from './ui/IconSymbol';

export const IconTest: React.FC = () => {
  const tabIcons = [
    { name: 'house.fill', label: 'Dashboard' },
    { name: 'magnifyingglass', label: 'Search' },
    { name: 'message.fill', label: 'Messages' },
    { name: 'person.fill', label: 'Profile' },
  ] as const;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Tab Bar Icons Test</Text>
      <View style={styles.iconsContainer}>
        {tabIcons.map((icon) => (
          <View key={icon.name} style={styles.iconItem}>
            <IconSymbol 
              name={icon.name} 
              size={28} 
              color="#007AFF" 
            />
            <Text style={styles.iconLabel}>{icon.label}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  iconsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  iconItem: {
    alignItems: 'center',
    margin: 10,
  },
  iconLabel: {
    marginTop: 5,
    fontSize: 12,
    color: '#666',
  },
});
