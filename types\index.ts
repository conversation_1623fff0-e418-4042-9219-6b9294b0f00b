// User Types
export type UserType = 'buyer' | 'seller' | 'agent' | 'property_manager' | 'investor' | 'renter';

export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  userType: UserType;
  profileImage?: string;
  preferences?: UserPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  location?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  propertyTypes?: PropertyType[];
  notifications?: {
    newListings: boolean;
    priceChanges: boolean;
    messages: boolean;
  };
}

// Property Types
export type PropertyType = 'plot' | 'flat' | 'building_with_land' | 'commercial' | 'rental';
export type PropertyStatus = 'available' | 'under_offer' | 'sold' | 'rented';

export interface Property {
  id: string;
  title: string;
  description: string;
  propertyType: PropertyType;
  status: PropertyStatus;
  price: number;
  location: {
    address: string;
    city: string;
    state: string;
    pincode: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  features: {
    bedrooms?: number;
    bathrooms?: number;
    area: number;
    areaUnit: 'sqft' | 'sqm' | 'acres';
    amenities?: string[];
  };
  images: string[];
  videos?: string[];
  virtualTourUrl?: string;
  legalDocuments: LegalDocument[];
  reraDetails?: RERADetails;
  ownerId: string;
  agentId?: string;
  createdAt: Date;
  updatedAt: Date;
  views: number;
  inquiries: number;
}

export interface LegalDocument {
  id: string;
  type: 'mutation_certificate' | 'dalil' | 'sketchmap' | 'revenue_receipt' | 'encumbrance_certificate' | 'corporation_holding_number' | 'occupancy_certificate';
  name: string;
  url: string;
  uploadedAt: Date;
  verified: boolean;
}

export interface RERADetails {
  registrationNumber: string;
  projectName: string;
  developerName: string;
  completionDate: Date;
  verified: boolean;
}

// Inquiry and Offer Types
export interface Inquiry {
  id: string;
  propertyId: string;
  buyerId: string;
  sellerId: string;
  agentId?: string;
  message: string;
  status: 'pending' | 'responded' | 'closed';
  createdAt: Date;
  responses?: InquiryResponse[];
}

export interface InquiryResponse {
  id: string;
  message: string;
  senderId: string;
  timestamp: Date;
}

export interface Offer {
  id: string;
  propertyId: string;
  buyerId: string;
  sellerId: string;
  amount: number;
  status: 'pending' | 'accepted' | 'rejected' | 'countered';
  message?: string;
  createdAt: Date;
  expiresAt: Date;
}

// Rental Types
export interface RentalApplication {
  id: string;
  propertyId: string;
  applicantId: string;
  propertyManagerId: string;
  status: 'pending' | 'approved' | 'rejected';
  documents: string[];
  monthlyIncome: number;
  employmentDetails: string;
  references: string[];
  createdAt: Date;
}

export interface LeaseAgreement {
  id: string;
  propertyId: string;
  tenantId: string;
  propertyManagerId: string;
  startDate: Date;
  endDate: Date;
  monthlyRent: number;
  securityDeposit: number;
  terms: string;
  documentUrl: string;
  status: 'active' | 'expired' | 'terminated';
}

export interface MaintenanceRequest {
  id: string;
  propertyId: string;
  tenantId: string;
  propertyManagerId: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed';
  images?: string[];
  assignedTo?: string;
  createdAt: Date;
  completedAt?: Date;
}

// Financial Types
export interface Payment {
  id: string;
  type: 'rent' | 'security_deposit' | 'maintenance' | 'property_purchase';
  amount: number;
  status: 'pending' | 'completed' | 'failed';
  payerId: string;
  receiverId: string;
  propertyId?: string;
  paymentMethod: string;
  transactionId?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface FinancialReport {
  id: string;
  propertyManagerId: string;
  period: {
    startDate: Date;
    endDate: Date;
  };
  income: number;
  expenses: number;
  netIncome: number;
  properties: string[];
  generatedAt: Date;
}

// Market Analysis Types
export interface MarketTrend {
  id: string;
  location: string;
  propertyType: PropertyType;
  averagePrice: number;
  priceChange: number;
  period: string;
  updatedAt: Date;
}

export interface PropertyValuation {
  id: string;
  propertyId: string;
  estimatedValue: number;
  confidence: number;
  factors: string[];
  generatedAt: Date;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  PropertyDetails: { propertyId: string };
  CreateListing: undefined;
  Profile: undefined;
  Search: undefined;
  Messages: undefined;
  Favorites: undefined;
};

export type TabParamList = {
  Dashboard: undefined;
  Search: undefined;
  Messages: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Filter Types
export interface PropertyFilters {
  propertyType?: PropertyType[];
  priceRange?: {
    min: number;
    max: number;
  };
  location?: string;
  bedrooms?: number;
  bathrooms?: number;
  areaRange?: {
    min: number;
    max: number;
  };
  amenities?: string[];
  reraCompliant?: boolean;
}
