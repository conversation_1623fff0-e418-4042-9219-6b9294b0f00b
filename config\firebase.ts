import { getApps, initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { getStorage } from 'firebase/storage';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyBLvCuofAPxsUeyKELqdaVoQxhdnqIaXYA",
  authDomain: "realestate-1f13c.firebaseapp.com",
  projectId: "realestate-1f13c",
  storageBucket: "realestate-1f13c.firebasestorage.app",
  messagingSenderId: "334673308273",
  appId: "1:334673308273:web:bf5cc624cc4205a2f149d0",
  measurementId: "G-45CR3YXZ3H"
};

// Initialize Firebase
let app;
if (getApps().length === 0) {
  app = initializeApp(firebaseConfig);
} else {
  app = getApps()[0];
}

// Initialize Firebase Auth
const auth = getAuth(app);

// Initialize Firestore
const db = getFirestore(app);

// Initialize Storage
const storage = getStorage(app);

// Initialize Functions
const functions = getFunctions(app);

export { auth, db, functions, storage };
export default app;
