import { PropertyService } from '@/services/propertyService';
import { Property, PropertyFilters, PropertyType } from '@/types';
import { Picker } from '@react-native-picker/picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    FlatList,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View,
} from 'react-native';

export default function SearchScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [propertyType, setPropertyType] = useState<PropertyType | ''>('');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [location, setLocation] = useState('');
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [reraCompliant, setReraCompliant] = useState(false);

  const propertyTypes: { label: string; value: PropertyType | '' }[] = [
    { label: 'All Types', value: '' },
    { label: 'Plot', value: 'plot' },
    { label: 'Flat', value: 'flat' },
    { label: 'Building with Land', value: 'building_with_land' },
    { label: 'Commercial', value: 'commercial' },
    { label: 'Rental', value: 'rental' },
  ];

  const handleSearch = async () => {
    setLoading(true);
    try {
      const filters: PropertyFilters = {};

      if (propertyType) {
        filters.propertyType = [propertyType];
      }

      if (location.trim()) {
        filters.location = location.trim();
      }

      if (minPrice || maxPrice) {
        filters.priceRange = {
          min: minPrice ? Number(minPrice) : 0,
          max: maxPrice ? Number(maxPrice) : Number.MAX_SAFE_INTEGER,
        };
      }

      if (reraCompliant) {
        filters.reraCompliant = true;
      }

      const result = await PropertyService.searchProperties(filters);

      if (result.success && result.data) {
        setProperties(result.data.properties);
      } else {
        console.error('Search failed:', result.error);
        setProperties([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setProperties([]);
    } finally {
      setLoading(false);
    }
  };

  // Load initial properties on component mount
  useEffect(() => {
    handleSearch();
  }, []);

  const clearFilters = () => {
    setSearchQuery('');
    setPropertyType('');
    setMinPrice('');
    setMaxPrice('');
    setLocation('');
    setReraCompliant(false);
    // Reload all properties
    handleSearch();
  };

  const renderPropertyItem = ({ item }: { item: Property }) => (
    <TouchableOpacity
      style={styles.propertyCard}
      onPress={() => router.push(`/property/${item.id}`)}
    >
      <Text style={styles.propertyTitle}>{item.title}</Text>
      <Text style={styles.propertyPrice}>₹{item.price.toLocaleString()}</Text>
      <Text style={styles.propertyLocation}>
        {item.location.address}, {item.location.city}
      </Text>
      <Text style={styles.propertyDetails}>
        {item.features.area} {item.features.areaUnit} • {item.propertyType}
        {item.features.bedrooms && ` • ${item.features.bedrooms} BHK`}
      </Text>
      {item.reraDetails && (
        <Text style={styles.reraText}>RERA: {item.reraDetails.registrationNumber}</Text>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Search Properties</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.searchSection}>
          <TextInput
            style={styles.searchInput}
            placeholder="Search properties..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />

          <View style={styles.filtersContainer}>
            <Text style={styles.filtersTitle}>Filters</Text>

            <View style={styles.filterRow}>
              <View style={styles.filterItem}>
                <Text style={styles.filterLabel}>Property Type</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={propertyType}
                    onValueChange={(itemValue) => setPropertyType(itemValue)}
                    style={styles.picker}
                  >
                    {propertyTypes.map((type) => (
                      <Picker.Item
                        key={type.value}
                        label={type.label}
                        value={type.value}
                      />
                    ))}
                  </Picker>
                </View>
              </View>
            </View>

            <View style={styles.filterRow}>
              <View style={styles.filterItem}>
                <Text style={styles.filterLabel}>Location</Text>
                <TextInput
                  style={styles.filterInput}
                  placeholder="Enter location"
                  value={location}
                  onChangeText={setLocation}
                />
              </View>
            </View>

            <View style={styles.filterRow}>
              <View style={[styles.filterItem, { flex: 1, marginRight: 10 }]}>
                <Text style={styles.filterLabel}>Min Price</Text>
                <TextInput
                  style={styles.filterInput}
                  placeholder="Min price"
                  value={minPrice}
                  onChangeText={setMinPrice}
                  keyboardType="numeric"
                />
              </View>
              <View style={[styles.filterItem, { flex: 1 }]}>
                <Text style={styles.filterLabel}>Max Price</Text>
                <TextInput
                  style={styles.filterInput}
                  placeholder="Max price"
                  value={maxPrice}
                  onChangeText={setMaxPrice}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.filterRow}>
              <View style={styles.checkboxContainer}>
                <TouchableOpacity
                  style={[styles.checkbox, reraCompliant && styles.checkboxChecked]}
                  onPress={() => setReraCompliant(!reraCompliant)}
                >
                  {reraCompliant && <Text style={styles.checkboxText}>✓</Text>}
                </TouchableOpacity>
                <Text style={styles.checkboxLabel}>RERA Compliant Only</Text>
              </View>
            </View>

            <View style={styles.buttonRow}>
              <TouchableOpacity style={styles.clearButton} onPress={clearFilters}>
                <Text style={styles.clearButtonText}>Clear</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.searchButton, loading && styles.searchButtonDisabled]}
                onPress={handleSearch}
                disabled={loading}
              >
                <Text style={styles.searchButtonText}>
                  {loading ? 'Searching...' : 'Search'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.resultsSection}>
          <Text style={styles.resultsTitle}>Properties ({properties.length})</Text>
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#007AFF" />
              <Text style={styles.loadingText}>Searching properties...</Text>
            </View>
          ) : (
            <FlatList
              data={properties}
              renderItem={renderPropertyItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>No properties found</Text>
                  <Text style={styles.emptySubtext}>Try adjusting your search filters</Text>
                </View>
              }
            />
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingTop: 50,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  searchSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    marginBottom: 20,
  },
  filtersContainer: {
    marginTop: 10,
  },
  filtersTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  filterRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  filterItem: {
    flex: 1,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  filterInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  picker: {
    height: 50,
  },
  buttonRow: {
    flexDirection: 'row',
    marginTop: 20,
  },
  clearButton: {
    flex: 1,
    backgroundColor: '#f0f0f0',
    padding: 16,
    borderRadius: 8,
    marginRight: 10,
  },
  clearButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  searchButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 8,
  },
  searchButtonDisabled: {
    backgroundColor: '#ccc',
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 4,
    marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxChecked: {
    backgroundColor: '#007AFF',
  },
  checkboxText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxLabel: {
    fontSize: 16,
    color: '#333',
  },
  resultsSection: {
    margin: 20,
    marginTop: 0,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  propertyCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  propertyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  propertyPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  propertyLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  propertyDetails: {
    fontSize: 12,
    color: '#999',
  },
  reraText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '600',
    marginTop: 4,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});
