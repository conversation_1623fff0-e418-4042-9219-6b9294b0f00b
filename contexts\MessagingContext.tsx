import { Conversation, MessagingService } from '@/services/messagingService';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuth } from './AuthContext';

interface MessagingContextType {
  conversations: Conversation[];
  loading: boolean;
  unreadCount: number;
  createConversation: (
    receiverId: string,
    receiverName: string,
    propertyId?: string,
    propertyTitle?: string
  ) => Promise<Conversation | null>;
  sendMessage: (conversationId: string, receiverId: string, receiverName: string, content: string) => Promise<boolean>;
  markAsRead: (conversationId: string) => Promise<void>;
  refreshConversations: () => Promise<void>;
}

const MessagingContext = createContext<MessagingContextType | undefined>(undefined);

export const useMessaging = () => {
  const context = useContext(MessagingContext);
  if (context === undefined) {
    throw new Error('useMessaging must be used within a MessagingProvider');
  }
  return context;
};

interface MessagingProviderProps {
  children: React.ReactNode;
}

export const MessagingProvider: React.FC<MessagingProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadConversations();
      // Subscribe to real-time updates
      const unsubscribe = MessagingService.subscribeToConversations(user.id, (updatedConversations) => {
        setConversations(updatedConversations);
      });

      return unsubscribe;
    } else {
      setConversations([]);
    }
  }, [user]);

  const loadConversations = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const result = await MessagingService.getUserConversations(user.id);
      if (result.success && result.data) {
        setConversations(result.data);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  const createConversation = async (
    receiverId: string,
    receiverName: string,
    propertyId?: string,
    propertyTitle?: string
  ): Promise<Conversation | null> => {
    if (!user) return null;

    try {
      const result = await MessagingService.createOrGetConversation(
        user.id,
        user.name,
        receiverId,
        receiverName,
        propertyId,
        propertyTitle
      );

      if (result.success && result.data) {
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('Error creating conversation:', error);
      return null;
    }
  };

  const sendMessage = async (
    conversationId: string,
    receiverId: string,
    receiverName: string,
    content: string
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      const result = await MessagingService.sendMessage(
        conversationId,
        user.id,
        user.name,
        receiverId,
        receiverName,
        content
      );

      return result.success;
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  };

  const markAsRead = async (conversationId: string) => {
    if (!user) return;

    try {
      await MessagingService.markMessagesAsRead(conversationId, user.id);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  };

  const refreshConversations = async () => {
    await loadConversations();
  };

  // Calculate total unread count
  const unreadCount = conversations.reduce((total, conversation) => {
    if (user && conversation.unreadCount[user.id]) {
      return total + conversation.unreadCount[user.id];
    }
    return total;
  }, 0);

  const value: MessagingContextType = {
    conversations,
    loading,
    unreadCount,
    createConversation,
    sendMessage,
    markAsRead,
    refreshConversations,
  };

  return (
    <MessagingContext.Provider value={value}>
      {children}
    </MessagingContext.Provider>
  );
};
