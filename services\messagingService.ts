import { db } from '@/config/firebase';
import { ApiResponse } from '@/types';
import {
    addDoc,
    collection,
    doc,
    getDocs,
    limit,
    onSnapshot,
    orderBy,
    query,
    serverTimestamp,
    updateDoc,
    where
} from 'firebase/firestore';

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  receiverId: string;
  receiverName: string;
  content: string;
  timestamp: Date;
  read: boolean;
  propertyId?: string;
  propertyTitle?: string;
}

export interface Conversation {
  id: string;
  participants: string[];
  participantNames: { [userId: string]: string };
  lastMessage: string;
  lastMessageTime: Date;
  propertyId?: string;
  propertyTitle?: string;
  unreadCount: { [userId: string]: number };
  createdAt: Date;
  updatedAt: Date;
}

export class MessagingService {
  private static readonly CONVERSATIONS_COLLECTION = 'conversations';
  private static readonly MESSAGES_COLLECTION = 'messages';

  // Create or get existing conversation
  static async createOrGetConversation(
    user1Id: string,
    user1Name: string,
    user2Id: string,
    user2Name: string,
    propertyId?: string,
    propertyTitle?: string
  ): Promise<ApiResponse<Conversation>> {
    try {
      // Check if conversation already exists
      const existingConversation = await this.findExistingConversation(user1Id, user2Id, propertyId);
      
      if (existingConversation.success && existingConversation.data) {
        return existingConversation;
      }

      // Create new conversation
      const conversationData = {
        participants: [user1Id, user2Id],
        participantNames: {
          [user1Id]: user1Name,
          [user2Id]: user2Name,
        },
        lastMessage: '',
        lastMessageTime: new Date(),
        propertyId: propertyId || undefined,
        propertyTitle: propertyTitle || undefined,
        unreadCount: {
          [user1Id]: 0,
          [user2Id]: 0,
        },
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      const docRef = await addDoc(collection(db, this.CONVERSATIONS_COLLECTION), conversationData);
      
      const newConversation: Conversation = {
        id: docRef.id,
        ...conversationData,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastMessageTime: new Date(),
      };

      return {
        success: true,
        data: newConversation,
        message: 'Conversation created successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Find existing conversation between two users
  private static async findExistingConversation(
    user1Id: string,
    user2Id: string,
    propertyId?: string
  ): Promise<ApiResponse<Conversation>> {
    try {
      let q = query(
        collection(db, this.CONVERSATIONS_COLLECTION),
        where('participants', 'array-contains', user1Id)
      );

      const querySnapshot = await getDocs(q);
      
      for (const docSnapshot of querySnapshot.docs) {
        const data = docSnapshot.data();
        const participants = data.participants as string[];
        
        // Check if both users are participants
        if (participants.includes(user2Id)) {
          // If propertyId is specified, check if it matches
          if (propertyId && data.propertyId !== propertyId) {
            continue;
          }
          
          const conversation: Conversation = {
            id: docSnapshot.id,
            participants: data.participants,
            participantNames: data.participantNames,
            lastMessage: data.lastMessage,
            lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
            propertyId: data.propertyId,
            propertyTitle: data.propertyTitle,
            unreadCount: data.unreadCount || {},
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
          };

          return {
            success: true,
            data: conversation,
          };
        }
      }

      return {
        success: false,
        error: 'Conversation not found',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Send a message
  static async sendMessage(
    conversationId: string,
    senderId: string,
    senderName: string,
    receiverId: string,
    receiverName: string,
    content: string
  ): Promise<ApiResponse<Message>> {
    try {
      const messageData = {
        conversationId,
        senderId,
        senderName,
        receiverId,
        receiverName,
        content,
        timestamp: serverTimestamp(),
        read: false,
      };

      const docRef = await addDoc(collection(db, this.MESSAGES_COLLECTION), messageData);

      // Update conversation with last message
      await this.updateConversationLastMessage(conversationId, content, receiverId);

      const newMessage: Message = {
        id: docRef.id,
        ...messageData,
        timestamp: new Date(),
      };

      return {
        success: true,
        data: newMessage,
        message: 'Message sent successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Update conversation with last message and increment unread count
  private static async updateConversationLastMessage(
    conversationId: string,
    lastMessage: string,
    receiverId: string
  ): Promise<void> {
    try {
      const conversationRef = doc(db, this.CONVERSATIONS_COLLECTION, conversationId);
      
      // Get current conversation to update unread count
      const conversationDoc = await getDocs(
        query(collection(db, this.CONVERSATIONS_COLLECTION), where('__name__', '==', conversationId))
      );
      
      if (!conversationDoc.empty) {
        const data = conversationDoc.docs[0].data();
        const currentUnreadCount = data.unreadCount || {};
        
        await updateDoc(conversationRef, {
          lastMessage,
          lastMessageTime: serverTimestamp(),
          updatedAt: serverTimestamp(),
          [`unreadCount.${receiverId}`]: (currentUnreadCount[receiverId] || 0) + 1,
        });
      }
    } catch (error) {
      console.error('Error updating conversation:', error);
    }
  }

  // Get user's conversations
  static async getUserConversations(userId: string): Promise<ApiResponse<Conversation[]>> {
    try {
      const q = query(
        collection(db, this.CONVERSATIONS_COLLECTION),
        where('participants', 'array-contains', userId),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const conversations: Conversation[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        conversations.push({
          id: doc.id,
          participants: data.participants,
          participantNames: data.participantNames,
          lastMessage: data.lastMessage,
          lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          unreadCount: data.unreadCount || {},
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        });
      });

      return {
        success: true,
        data: conversations,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Get messages for a conversation
  static async getConversationMessages(
    conversationId: string,
    limitCount: number = 50
  ): Promise<ApiResponse<Message[]>> {
    try {
      const q = query(
        collection(db, this.MESSAGES_COLLECTION),
        where('conversationId', '==', conversationId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const querySnapshot = await getDocs(q);
      const messages: Message[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          conversationId: data.conversationId,
          senderId: data.senderId,
          senderName: data.senderName,
          receiverId: data.receiverId,
          receiverName: data.receiverName,
          content: data.content,
          timestamp: data.timestamp?.toDate() || new Date(),
          read: data.read,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
        });
      });

      // Reverse to show oldest first
      messages.reverse();

      return {
        success: true,
        data: messages,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Mark messages as read
  static async markMessagesAsRead(conversationId: string, userId: string): Promise<ApiResponse<void>> {
    try {
      // Update unread count in conversation
      const conversationRef = doc(db, this.CONVERSATIONS_COLLECTION, conversationId);
      await updateDoc(conversationRef, {
        [`unreadCount.${userId}`]: 0,
      });

      return {
        success: true,
        message: 'Messages marked as read',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Subscribe to real-time messages
  static subscribeToMessages(
    conversationId: string,
    callback: (messages: Message[]) => void
  ): () => void {
    const q = query(
      collection(db, this.MESSAGES_COLLECTION),
      where('conversationId', '==', conversationId),
      orderBy('timestamp', 'asc')
    );

    return onSnapshot(q, (querySnapshot) => {
      const messages: Message[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          conversationId: data.conversationId,
          senderId: data.senderId,
          senderName: data.senderName,
          receiverId: data.receiverId,
          receiverName: data.receiverName,
          content: data.content,
          timestamp: data.timestamp?.toDate() || new Date(),
          read: data.read,
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
        });
      });
      callback(messages);
    });
  }

  // Subscribe to real-time conversations
  static subscribeToConversations(
    userId: string,
    callback: (conversations: Conversation[]) => void
  ): () => void {
    const q = query(
      collection(db, this.CONVERSATIONS_COLLECTION),
      where('participants', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    return onSnapshot(q, (querySnapshot) => {
      const conversations: Conversation[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        conversations.push({
          id: doc.id,
          participants: data.participants,
          participantNames: data.participantNames,
          lastMessage: data.lastMessage,
          lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
          propertyId: data.propertyId,
          propertyTitle: data.propertyTitle,
          unreadCount: data.unreadCount || {},
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        });
      });
      callback(conversations);
    });
  }
}
