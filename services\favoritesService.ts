import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  collection,
  addDoc,
  deleteDoc,
  getDocs,
  query,
  where,
  doc,
} from 'firebase/firestore';
import { db } from '@/config/firebase';
import { Property, ApiResponse } from '@/types';

export class FavoritesService {
  private static readonly COLLECTION_NAME = 'favorites';
  private static readonly STORAGE_KEY = 'user_favorites';

  // Add property to favorites
  static async addToFavorites(userId: string, propertyId: string): Promise<ApiResponse<void>> {
    try {
      // Add to Firestore
      await addDoc(collection(db, this.COLLECTION_NAME), {
        userId,
        propertyId,
        createdAt: new Date(),
      });

      // Update local storage
      await this.updateLocalFavorites(userId, propertyId, 'add');

      return {
        success: true,
        message: 'Property added to favorites',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Remove property from favorites
  static async removeFromFavorites(userId: string, propertyId: string): Promise<ApiResponse<void>> {
    try {
      // Remove from Firestore
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('userId', '==', userId),
        where('propertyId', '==', propertyId)
      );
      
      const querySnapshot = await getDocs(q);
      querySnapshot.forEach(async (docSnapshot) => {
        await deleteDoc(doc(db, this.COLLECTION_NAME, docSnapshot.id));
      });

      // Update local storage
      await this.updateLocalFavorites(userId, propertyId, 'remove');

      return {
        success: true,
        message: 'Property removed from favorites',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Get user's favorite property IDs
  static async getFavoritePropertyIds(userId: string): Promise<ApiResponse<string[]>> {
    try {
      // Try to get from local storage first
      const localFavorites = await this.getLocalFavorites(userId);
      if (localFavorites.length > 0) {
        return {
          success: true,
          data: localFavorites,
        };
      }

      // If not in local storage, get from Firestore
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('userId', '==', userId)
      );
      
      const querySnapshot = await getDocs(q);
      const favoriteIds: string[] = [];
      
      querySnapshot.forEach((doc) => {
        favoriteIds.push(doc.data().propertyId);
      });

      // Cache in local storage
      await this.setLocalFavorites(userId, favoriteIds);

      return {
        success: true,
        data: favoriteIds,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        data: [],
      };
    }
  }

  // Check if property is in favorites
  static async isPropertyFavorited(userId: string, propertyId: string): Promise<boolean> {
    try {
      const result = await this.getFavoritePropertyIds(userId);
      if (result.success && result.data) {
        return result.data.includes(propertyId);
      }
      return false;
    } catch (error) {
      console.error('Error checking if property is favorited:', error);
      return false;
    }
  }

  // Local storage helpers
  private static async getLocalFavorites(userId: string): Promise<string[]> {
    try {
      const stored = await AsyncStorage.getItem(`${this.STORAGE_KEY}_${userId}`);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting local favorites:', error);
      return [];
    }
  }

  private static async setLocalFavorites(userId: string, favoriteIds: string[]): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.STORAGE_KEY}_${userId}`, JSON.stringify(favoriteIds));
    } catch (error) {
      console.error('Error setting local favorites:', error);
    }
  }

  private static async updateLocalFavorites(
    userId: string,
    propertyId: string,
    action: 'add' | 'remove'
  ): Promise<void> {
    try {
      const currentFavorites = await this.getLocalFavorites(userId);
      let updatedFavorites: string[];

      if (action === 'add') {
        updatedFavorites = currentFavorites.includes(propertyId)
          ? currentFavorites
          : [...currentFavorites, propertyId];
      } else {
        updatedFavorites = currentFavorites.filter(id => id !== propertyId);
      }

      await this.setLocalFavorites(userId, updatedFavorites);
    } catch (error) {
      console.error('Error updating local favorites:', error);
    }
  }

  // Clear local favorites (useful for logout)
  static async clearLocalFavorites(userId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${this.STORAGE_KEY}_${userId}`);
    } catch (error) {
      console.error('Error clearing local favorites:', error);
    }
  }

  // Sync local favorites with Firestore (useful for offline/online sync)
  static async syncFavorites(userId: string): Promise<ApiResponse<void>> {
    try {
      // Get favorites from Firestore
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('userId', '==', userId)
      );
      
      const querySnapshot = await getDocs(q);
      const firestoreFavorites: string[] = [];
      
      querySnapshot.forEach((doc) => {
        firestoreFavorites.push(doc.data().propertyId);
      });

      // Update local storage with Firestore data
      await this.setLocalFavorites(userId, firestoreFavorites);

      return {
        success: true,
        message: 'Favorites synced successfully',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }
}
